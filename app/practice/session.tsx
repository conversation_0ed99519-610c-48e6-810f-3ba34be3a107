import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect } from 'react';
import { Al<PERSON>, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Ionicons } from '@expo/vector-icons';
import { Button } from '../../src/components/common';
import {
  OptionsList,
  QuestionCard
} from '../../src/components/question';
import { useRealtimeAnswer } from '../../src/hooks/useRealtimeAnswer';
import { usePracticeStore } from '../../src/store/usePracticeStore';
import { COLORS } from '../../src/utils/constants';

export default function PracticeSessionScreen() {
  const quickAnswerMode = true; // Always use quick answer mode

  const {
    session,
    currentQuestion,
    selectedAnswer,
    showAnswer,
    stats,
    isActive,
    selectAnswer,
    submitAnswer,
    nextQuestion,
    endSession,
  } = usePracticeStore();

  const { recordAnswer } = useRealtimeAnswer();

  // Minimalistic stats component
  const renderMinimalisticStats = () => {
    const totalAnswered = stats.answeredCount;
    const correctCount = stats.correctCount;
    const wrongCount = stats.wrongCount;
    const correctRatio = totalAnswered > 0 ? correctCount / totalAnswered : 0;
    const remainingQuestions = session ? Math.max(0, session.questions.length - session.currentIndex) : 0;

    return (
      <View style={styles.minimalisticStats}>
        <Text style={styles.correctNumber}>{correctCount}</Text>
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBg}>
            <View
              style={[
                styles.progressBarCorrect,
                { width: `${correctRatio * 100}%` },
              ]}
            />
          </View>
        </View>
        <Text style={styles.wrongNumber}>{wrongCount}</Text>
        <Text style={styles.remainingCount}>剩{remainingQuestions}題</Text>
      </View>
    );
  };

  useEffect(() => {
    if (!session || !isActive) {
      router.dismissTo('/');
    }
  }, [session, isActive]);

  if (!session || !currentQuestion) {
    return null;
  }

  const handleAnswerSelect = (answerIndex: number) => {
    if (showAnswer) return;
    selectAnswer(answerIndex);

    if (quickAnswerMode) {
      handleSubmitAnswer(answerIndex);
    }
  };

  const handleSubmitAnswer = async (answer?: number) => {
    const finalAnswer = answer ?? selectedAnswer;
    if (finalAnswer === null || showAnswer) return;

    // Get current practice store state to access answerStartTime
    const state = usePracticeStore.getState();
    const answerTime = state.answerStartTime;
    const timeSpent = answerTime ? Math.round((Date.now() - answerTime.getTime()) / 1000) : 0;

    submitAnswer();

    // Record answer in database
    try {
      const correctOptionIndex = currentQuestion.options.findIndex(opt => opt.isCorrect);
      const selectedOptionLabel = ['A', 'B', 'C', 'D'][finalAnswer];
      const correctOptionLabel = ['A', 'B', 'C', 'D'][correctOptionIndex];

      await recordAnswer({
        questionId: currentQuestion.id,
        volume: currentQuestion.volume,
        chapter: 1, // Default chapter
        isCorrect: currentQuestion.options[finalAnswer]?.isCorrect || false,
        mode: 'practice',
        sessionId: session.id,
        selectedOption: selectedOptionLabel as 'A' | 'B' | 'C' | 'D',
        correctOption: correctOptionLabel as 'A' | 'B' | 'C' | 'D',
        timeSpent: Math.max(1, timeSpent), // Minimum 1 second
      });
    } catch (error) {
      console.error('Failed to record answer:', error);
      // Don't block the UI if recording fails
    }
  };

  const handleNext = () => {
    if (session.currentIndex < session.questions.length - 1) {
      nextQuestion();
    } else {
      // Reached end of current question set - offer to continue or finish
      Alert.alert(
        '已完成當前題目',
        `您已完成當前所有題目！\n\n正確率：${stats.accuracy.toFixed(1)}%\n答對：${stats.correctCount} 題\n答錯：${stats.wrongCount} 題\n\n建議繼續練習其他題目來鞏固知識。`,
        [
          {
            text: '結束練習',
            onPress: () => {
              endSession();
              router.dismissTo('/');
            },
            style: 'cancel'
          },
          {
            text: '繼續練習',
            onPress: () => {
              // End current session and go back to home for new practice
              endSession();
              router.dismissTo('/');
            },
          },
        ]
      );
    }
  };



  const handleEndSession = () => {
    Alert.alert(
      '結束練習',
      `本次練習統計：\n\n已完成：${stats.answeredCount} 題\n答對：${stats.correctCount} 題\n答錯：${stats.wrongCount} 題\n正確率：${stats.accuracy.toFixed(1)}%\n\n確定要結束練習嗎？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '結束練習',
          onPress: () => {
            endSession();
            router.dismissTo('/');
          },
        },
      ]
    );
  };

  const isLastQuestion = session.currentIndex === session.questions.length - 1;

  return (
    <SafeAreaView style={styles.container} edges={['top', 'bottom', 'left', 'right']}>
      <StatusBar style="auto" />

      {/* Header with close button and quick answer toggle */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity onPress={() => handleEndSession()} className="pr-2">
            <Ionicons name="chevron-back" size={28} color="black" />
          </TouchableOpacity>
        </View>

        {/* Centered Progress Bar */}
        <View style={styles.headerCenter}>
          {renderMinimalisticStats()}
        </View>

        <View style={styles.headerRight}>
          {/* Quick answer mode is now always enabled */}
        </View>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <QuestionCard
          question={currentQuestion}
          questionNumber={session.currentIndex + 1}
        />

        <OptionsList
          options={currentQuestion.options}
          selectedOption={selectedAnswer !== null ? selectedAnswer : undefined}
          onOptionSelect={handleAnswerSelect}
          showAnswer={showAnswer}
          disabled={showAnswer}
        />
      </ScrollView>

      {/* Sticky bottom single action */}
      <View style={styles.stickyBottomContainer}>
        <Button
          title={isLastQuestion ? "繼續練習" : "下一題"}
          onPress={handleNext}
          disabled={!showAnswer}
          style={styles.fullWidthButton}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  progressContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.TEXT,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.SECONDARY_TEXT,
    marginTop: 4,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  actionContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  navigationButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  navButton: {
    flex: 1,
  },
  submitButton: {
    flex: 2,
  },
  endButton: {
    width: '100%',
  },
  disabledButton: {
    opacity: 0.5,
  },
  // New styles for updated UI
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: COLORS.BACKGROUND,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  headerLeft: {
    flex: 1,
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 2,
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  closeButton: {
    width: 36,
    height: 36,
    padding: 0,
    margin: 0,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: COLORS.THEME,
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  closeButtonText: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.THEME,
  },
  quickAnswerToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  quickAnswerLabel: {
    fontSize: 14,
    color: COLORS.TEXT,
  },
  toggle: {
    width: 44,
    height: 24,
    borderRadius: 12,
    backgroundColor: COLORS.BORDER,
    padding: 2,
  },
  toggleActive: {
    backgroundColor: COLORS.THEME,
  },
  quickAnswerBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    minHeight: 0,
    backgroundColor: 'transparent',
    borderColor: COLORS.THEME,
  },
  quickAnswerBadgeActive: {
    backgroundColor: COLORS.THEME,
  },
  quickAnswerBadgeText: {
    fontSize: 12,
    color: COLORS.THEME_TEXT,
    fontWeight: '600',
  },
  quickAnswerBadgeTextActive: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '600',
  },
  minimalisticStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    width: '100%',
  },
  correctNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.SUCCESS,
    minWidth: 24,
    textAlign: 'center',
  },
  wrongNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.ERROR,
    minWidth: 24,
    textAlign: 'center',
  },
  remainingCount: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.TEXT_LIGHT,
    marginLeft: 8,
  },
  progressBarContainer: {
    flex: 1,
    height: 8,
    backgroundColor: COLORS.BORDER,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarBg: {
    flex: 1,
    backgroundColor: COLORS.ERROR,
  },
  progressBarCorrect: {
    height: '100%',
    backgroundColor: COLORS.SUCCESS,
  },
  stickyBottomContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: COLORS.BACKGROUND,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  actionsStack: {
    gap: 12,
  },
  fullWidthButton: {
    width: '100%',
    backgroundColor: COLORS.THEME,
  },
});