import { router } from 'expo-router';
import React, { useState } from 'react';
import { Al<PERSON>, ScrollView, StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { <PERSON><PERSON>, Card, LoadingSpinner } from '../../src/components/common';
import { useStatistics } from '../../src/hooks/useStatistics';
import { QuestionManager } from '../../src/services/questions/manager';
import { usePracticeStore } from '../../src/store/usePracticeStore';
import { PracticeConfig } from '../../src/types/session';
import { COLORS, VOLUMES } from '../../src/utils/constants';

export default function PracticeOptionsScreen() {
  const [loading, setLoading] = useState(false);
  const [selectedVolumes, setSelectedVolumes] = useState<number[]>([]);
  const [includeUnseen] = useState(true);
  const [practiceMode] = useState<'sequential' | 'random'>('random');
  const { startSession } = usePracticeStore();
  const { volumeDisplayStats } = useStatistics();
  const actionDisabled = selectedVolumes.length === 0;

  const startPractice = async (config: PracticeConfig) => {
    setLoading(true);
    
    try {
      const questions = await QuestionManager.generatePracticeQuestions(config);
      
      if (questions.length === 0) {
        Alert.alert('沒有題目', '所選範圍內沒有找到題目，請選擇其他選項。');
        return;
      }

      // Check if all questions in selected volumes have been mastered
      let allMastered = false;
      if (config.includeUnseen && volumeDisplayStats) {
        const selectedVolumeStats = volumeDisplayStats.filter(stats => 
          config.volumes.includes(stats.volume)
        );
        allMastered = selectedVolumeStats.every(stats => 
          stats.correct >= stats.total
        );
      }

      if (allMastered) {
        Alert.alert(
          '恭喜完成！', 
          '您已掌握所選冊別的所有題目！現在將開始複習模式，您可以重新練習來保持熟練度。',
          [{ text: '開始複習', onPress: () => {
            startSession(config, questions);
            router.replace('/practice/session');
          }}]
        );
      } else {
        startSession(config, questions);
        router.replace('/practice/session');
      }
    } catch (error) {
      console.error('Failed to start practice:', error);
      Alert.alert('錯誤', '無法載入練習題目，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  const toggleVolumeSelection = (volume: number) => {
    // Only allow volume 3 to be selected
    if (volume !== 3) {
      Alert.alert('即將推出', '此冊別練習功能即將推出，敬請期待！');
      return;
    }
    
    setSelectedVolumes(prev => {
      if (prev.includes(volume)) {
        return prev.filter(v => v !== volume);
      } else {
        return [...prev, volume].sort();
      }
    });
  };


  const handleStartPractice = () => {
    if (selectedVolumes.length === 0) {
      Alert.alert('請選擇冊別', '請至少選擇一個冊別進行練習。');
      return;
    }

    const config: PracticeConfig = {
      volumes: selectedVolumes,
      chapter: null,
      mode: practiceMode,
      includeWrongQuestions: false,
      includeBookmarked: false,
      includeUnseen,
    };

    startPractice(config);
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner message="正在載入題目..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom', 'left', 'right']}>
      {/* <StatusBar style="auto" /> */}
      <ScrollView
        style={styles.scrollView}
      >
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>選擇練習冊別</Text>
            <Text style={styles.sectionSubtitle}>可選擇一個或多個冊別進行練習</Text>
          </View>

          <View style={styles.volumeGrid}>
            {Array.from({ length: VOLUMES.TOTAL }, (_, i) => i + 1).map(volume => {
              const isSelected = selectedVolumes.includes(volume);
              const isDisabled = volume !== 3;
              const volumeStats = volumeDisplayStats?.find(v => v.volume === volume);
              const accuracy = volumeStats ? 
                (volumeStats.correct / Math.max(1, volumeStats.correct + volumeStats.wrong) * 100) : 0;
              
              return (
                <Card
                  key={volume}
                  style={[
                    styles.volumeCard,
                    isSelected && styles.selectedVolumeCard,
                    isDisabled && styles.disabledVolumeCard
                  ] as any}
                  onPress={() => toggleVolumeSelection(volume)}
                >
                  <View style={styles.volumeHeader}>
                    <Text style={[
                      styles.volumeTitle,
                      isSelected && styles.selectedVolumeTitle,
                      isDisabled && styles.disabledText
                    ]}>
                      第 {volume} 冊
                    </Text>
                    <View style={[
                      styles.checkbox,
                      isSelected && styles.checkedBox,
                      isDisabled && styles.disabledCheckbox
                    ]}>
                      {isSelected && <Text style={styles.checkmark}>✓</Text>}
                    </View>
                  </View>
                  <Text style={[
                    styles.volumeSubtitle,
                    isSelected && styles.selectedVolumeSubtitle,
                    isDisabled && styles.disabledText
                  ]}>
                    {volumeStats?.title || VOLUMES.NAMES[volume as keyof typeof VOLUMES.NAMES]}
                  </Text>
                  
                  {isDisabled ? (
                    <Text style={[styles.volumeDescription, styles.comingSoonText]}>
                      即將推出
                    </Text>
                  ) : volumeStats && (volumeStats.correct + volumeStats.wrong > 0) ? (
                    <View style={styles.statsContainer}>
                      <Text style={[
                        styles.accuracyText,
                        isSelected && styles.selectedVolumeDescription,
                        { color: accuracy >= 80 ? COLORS.SUCCESS : accuracy >= 60 ? COLORS.WARNING : COLORS.ERROR }
                      ]}>
                        正確率: {accuracy.toFixed(0)}%
                      </Text>
                      <Text style={[
                        styles.volumeDescription,
                        isSelected && styles.selectedVolumeDescription
                      ]}>
                        已練習 {volumeStats.correct + volumeStats.wrong} / {volumeStats.total} 題
                      </Text>
                    </View>
                  ) : (
                    <Text style={[
                      styles.volumeDescription,
                      isSelected && styles.selectedVolumeDescription
                    ]}>
                      共 {volumeStats?.total || VOLUMES.COUNTS[volume as keyof typeof VOLUMES.COUNTS]} 道題目 · 未開始練習
                    </Text>
                  )}
                </Card>
              );
            })}
          </View>
        </View>
      </ScrollView>

      {/* Sticky Start Practice Button */}
      <View style={styles.stickyBottomContainer}>
        <Button
          title={selectedVolumes.length > 0 ? `開始練習 (已選 ${selectedVolumes.length} 冊)` : '請選擇冊別'}
          onPress={handleStartPractice}
          disabled={actionDisabled}
          style={[styles.actionButton, actionDisabled ? styles.actionButtonDisabled : styles.actionButtonActive] as any}
          textStyle={actionDisabled ? styles.actionButtonDisabledText : styles.actionButtonActiveText}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    marginBottom: 16,
    paddingTop: 16,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 6,
    color: COLORS.TEXT,
    letterSpacing: -0.3,
  },
  sectionSubtitle: {
    fontSize: 15,
    color: COLORS.TEXT_LIGHT,
    lineHeight: 20,
  },
  volumeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  volumeCard: {
    width: '48%',
    marginBottom: 16,
    borderWidth: 2,
    borderColor: 'transparent',
    borderRadius: 16,
    backgroundColor: COLORS.CARD_BACKGROUND,
    shadowColor: COLORS.SHADOW,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
    padding: 16,
  },
  selectedVolumeCard: {
    borderColor: COLORS.ACCENT,
    borderWidth: 2,
    shadowOpacity: 0.12,
    elevation: 4,
  },
  volumeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  volumeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT,
  },
  selectedVolumeTitle: {
    color: COLORS.TEXT,
    fontWeight: '700',
  },
  volumeSubtitle: {
    fontSize: 14,
    color: COLORS.TEXT,
    marginBottom: 8,
  },
  selectedVolumeSubtitle: {
    color: COLORS.TEXT,
  },
  volumeDescription: {
    fontSize: 12,
    color: COLORS.SECONDARY_TEXT,
  },
  selectedVolumeDescription: {
    color: COLORS.SECONDARY_TEXT,
  },
  checkbox: {
    width: 22,
    height: 22,
    borderRadius: 11,
    borderWidth: 2,
    borderColor: COLORS.BORDER,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.CARD_BACKGROUND,
  },
  checkedBox: {
    backgroundColor: COLORS.ACCENT,
    borderColor: COLORS.ACCENT,
  },
  checkmark: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  optionCard: {
    marginBottom: 12,
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.TEXT,
  },
  optionSubLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.TEXT,
  },
  optionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 12,
  },
  advancedOptions: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  startButton: {
    marginTop: 8,
  },
  disabledButton: {
    opacity: 0.5,
  },
  scrollView: {
    flex: 1,
  },
  stickyBottomContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.BACKGROUND,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  actionButton: {
    width: '100%',
    borderWidth: 0,
  },
  actionButtonActive: {
    backgroundColor: COLORS.ACCENT,
  },
  actionButtonDisabled: {
    backgroundColor: COLORS.THEME_DISABLED,
  },
  actionButtonActiveText: {
    color: 'white',
    fontWeight: '600',
  },
  actionButtonDisabledText: {
    color: 'white',
  },
  statsContainer: {
    marginTop: 4,
  },
  accuracyText: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
  },
  disabledVolumeCard: {
    opacity: 0.8,
    backgroundColor: COLORS.CARD_BACKGROUND_DISABLED,
  },
  disabledText: {
    opacity: 0.7,
    color: COLORS.SECONDARY_TEXT,
  },
  disabledCheckbox: {
    borderColor: COLORS.SECONDARY_TEXT,
    backgroundColor: COLORS.BORDER,
  },
  comingSoonText: {
    color: COLORS.WARNING,
    fontWeight: '600',
    fontSize: 12,
  },
});