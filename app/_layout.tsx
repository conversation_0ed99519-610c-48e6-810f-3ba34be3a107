import { COLORS } from '@/src/utils/constants';
import { Ionicons } from '@expo/vector-icons';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { router, Stack } from 'expo-router';
import { useEffect } from 'react';
import { TouchableOpacity, useColorScheme } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import 'react-native-reanimated';
import { initializeDatabase } from '../src/services/database';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    // Initialize database when app starts
    initializeDatabase().catch(error => {
      console.error('Failed to initialize database on app start:', error);
    });
  }, []);

  if (!loaded) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <BottomSheetModalProvider>
          <Stack screenOptions={{
            headerStyle: {
              backgroundColor: COLORS.BACKGROUND,
            },
            headerShadowVisible: false,
          }}>
            <Stack.Screen name="index" options={{
              title: '首頁',
              headerShown: false
            }} />
            {/* 練習 */}
            <Stack.Screen 
              name="practice/options" 
              options={{ 
                title: '',
                headerShown: true,
                headerLeft: () => ( 
                  <TouchableOpacity onPress={() => router.back()} className="pr-2">
                    <Ionicons name="chevron-back" size={28} color="black" />
                  </TouchableOpacity>
                ),
              }} 
            />
            <Stack.Screen 
              name="practice/session" 
              options={{ 
                title: '',
                headerShown: false,
                gestureEnabled: false,
              }} 
            />
            {/* 錯題回顧 */}
            <Stack.Screen 
              name="review/index" 
              options={{ 
                title: '',
                headerShown: true,
                headerLeft: () => (
                  <TouchableOpacity onPress={() => router.back()} className="pr-2">
                    <Ionicons name="chevron-back" size={28} color="black" />
                  </TouchableOpacity>
                ),
              }} 
            />
            <Stack.Screen 
              name="review/session" 
              options={{ 
                title: '',
                headerShown: false,
                gestureEnabled: false,
              }} 
            />
            {/* 模擬考試 */}
            <Stack.Screen 
              name="exam/index" 
              options={{ 
                title: '',
                headerShown: true,
                headerLeft: () => (
                  <TouchableOpacity onPress={() => router.back()} className="pr-2">
                    <Ionicons name="chevron-back" size={28} color="black" />
                  </TouchableOpacity>
                ),
              }} 
            />
            <Stack.Screen 
              name="exam/session" 
              options={{ 
                title: '',
                headerShown: false,
                gestureEnabled: false,
              }} 
            />
            <Stack.Screen 
              name="exam/result" 
              options={{ 
                title: '',
                headerShown: false,
                gestureEnabled: false,
              }} 
            />
            <Stack.Screen name="+not-found" />
          </Stack>
        </BottomSheetModalProvider>
      </ThemeProvider>
    </GestureHandlerRootView>
  );
}
