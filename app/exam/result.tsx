import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useRef, useState } from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Ionicons } from '@expo/vector-icons';
import { Button, Card } from '../../src/components/common';
import { OptionsList, QuestionCard, QuestionNumbersBar } from '../../src/components/question';
import { useExamStore } from '../../src/store/useExamStore';
import { ProcessedQuestion } from '../../src/types/question';
import { COLORS } from '../../src/utils/constants';

export default function ExamResultScreen() {
  const { session, examResult } = useExamStore();

  // All hooks must be called before any early returns
  const [reviewMode, setReviewMode] = useState(false);
  const [currentReviewIndex, setCurrentReviewIndex] = useState(0);
  const [scrollViewWidth, setScrollViewWidth] = useState(0);
  const [currentScrollX, setCurrentScrollX] = useState(0);
  const [isManualScrolling, setIsManualScrolling] = useState(false);
  const [showQuestionNumbers, setShowQuestionNumbers] = useState(false);
  const questionNumbersScrollRef = useRef<ScrollView>(null);
  const manualScrollTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Auto-scroll to current question in question numbers bar during review (intelligent scrolling)
  useEffect(() => {
    if (reviewMode && showQuestionNumbers && questionNumbersScrollRef.current && scrollViewWidth > 0 && !isManualScrolling) {
      const questionWidth = 36; // Width of each question number button
      const questionGap = 8; // Gap between buttons
      const itemWidth = questionWidth + questionGap;
      const currentQuestionPosition = currentReviewIndex * itemWidth;

      // Calculate visible area with buffer
      const buffer = itemWidth * 1.5; // Show 1.5 questions buffer on each side
      const visibleStart = currentScrollX;
      const visibleEnd = currentScrollX + scrollViewWidth;

      // Check if current question is outside visible area (with buffer)
      const questionStart = currentQuestionPosition;
      const questionEnd = currentQuestionPosition + questionWidth;

      const needsScrolling = questionStart < visibleStart + buffer || questionEnd > visibleEnd - buffer;

      if (needsScrolling) {
        // Center the current question in the viewport
        const scrollPosition = Math.max(0, currentQuestionPosition - (scrollViewWidth / 2) + (questionWidth / 2));

        setTimeout(() => {
          questionNumbersScrollRef.current?.scrollTo({
            x: scrollPosition,
            animated: true,
          });
        }, 100);
      }
    }
  }, [currentReviewIndex, reviewMode, showQuestionNumbers, scrollViewWidth, currentScrollX, isManualScrolling]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (manualScrollTimeoutRef.current) {
        clearTimeout(manualScrollTimeoutRef.current);
      }
    };
  }, []);

  // Early return AFTER all hooks
  if (!examResult || !session) {
    return null;
  }

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}時${minutes}分${remainingSeconds}秒`;
    }
    return `${minutes}分${remainingSeconds}秒`;
  };

  const getFailureReasonText = () => {
    if (examResult.failureReason === 'too_many_wrong_total') {
      return `總錯誤題數超過限制（${examResult.wrongCount} > 8）`;
    }
    if (examResult.failureReason === 'too_many_wrong_per_set') {
      const failedSets = [];
      if (examResult.setResults.set3.wrongCount > 2) failedSets.push('第3冊');
      if (examResult.setResults.set4.wrongCount > 2) failedSets.push('第4冊');
      if (examResult.setResults.set5.wrongCount > 2) failedSets.push('第5冊');
      return `${failedSets.join('、')}錯誤題數超過限制（>2題）`;
    }
    return '';
  };

  const getAllQuestionsForReview = (): {
    question: ProcessedQuestion;
    userAnswer: number;
    isCorrect: boolean;
    setName: string;
  }[] => {
    if (!session) {
      return [];
    }

    const allQuestions: {
      question: ProcessedQuestion;
      userAnswer: number;
      isCorrect: boolean;
      setName: string;
    }[] = [];

    // Use the session questions to maintain the exact exam order
    session.questions.forEach((question, examIndex) => {
      // Get the user's answer for this question (stored as original option index)
      const originalUserAnswer = session.answers[examIndex];

      // Keep the shuffled options order as shown during the exam
      // The question.options are already in the shuffled order from the exam session
      const displayQuestion = question;

      // Convert original answer index to displayed answer index for result display
      let displayUserAnswer = -1;
      if (originalUserAnswer !== undefined) {
        const optionMap = session.paperMap.optionMaps[examIndex];
        displayUserAnswer = optionMap ? optionMap.originalToDisplay[originalUserAnswer] : originalUserAnswer;
      }

      // Get set information from paper map
      const questionMap = session.paperMap.questionMap[examIndex];
      const setNumber = questionMap ? questionMap.setNumber : 1;
      const setNames = ['第1冊', '第2冊', '第3冊', '第4冊', '第5冊'];
      const setName = setNames[setNumber - 1];

      // Check if answer is correct using the original answer index
      // We need to check correctness against the original options, not the shuffled display
      let isCorrect = false;
      if (originalUserAnswer !== undefined) {
        // Find the correct option in the shuffled options
        const correctShuffledIndex = displayQuestion.options.findIndex(opt => opt.isCorrect);
        if (correctShuffledIndex !== -1) {
          const optionMap = session.paperMap.optionMaps[examIndex];
          const correctOriginalIndex = optionMap ? optionMap.displayToOriginal[correctShuffledIndex] : correctShuffledIndex;
          isCorrect = originalUserAnswer === correctOriginalIndex;
        }
      }

      allQuestions.push({
        question: displayQuestion,
        userAnswer: displayUserAnswer,
        isCorrect,
        setName,
      });
    });

    return allQuestions;
  };

  const handleGoHome = () => {
    // Just navigate cleanly - next exam will reset state anyway
    router.dismissTo('/');
  };

  const handleStartReview = () => {
    setReviewMode(true);
    setCurrentReviewIndex(0);
  };

  const renderResult = () => (
    <View style={styles.content}>
      {/* Result Header */}
      <View style={styles.resultHeader}>
        <Text style={styles.resultIcon}>
          {examResult.isPassed ? '🎉' : '😞'}
        </Text>
        <Text style={[styles.resultTitle, examResult.isPassed ? styles.passedTitle : styles.failedTitle]}>
          {examResult.isPassed ? '恭喜合格！' : '很遺憾，未能合格'}
        </Text>
        <Text style={styles.resultSubtitle}>
          {examResult.isPassed
            ? '您已通過澳門考車理論考試'
            : '請繼續努力，下次一定可以'}
        </Text>
      </View>

      {/* Score Summary */}
      <Card style={styles.summaryCard}>
        <Text style={styles.cardTitle}>考試成績</Text>

        <View style={styles.scoreRow}>
          <Text style={styles.scoreLabel}>總分</Text>
          <Text style={styles.scoreValue}>
            {examResult.score}/{examResult.totalQuestions}
          </Text>
        </View>

        <View style={styles.scoreRow}>
          <Text style={styles.scoreLabel}>正確率</Text>
          <Text style={styles.scoreValue}>
            {Math.round((examResult.score / examResult.totalQuestions) * 100)}%
          </Text>
        </View>

        <View style={styles.scoreRow}>
          <Text style={styles.scoreLabel}>用時</Text>
          <Text style={styles.scoreValue}>
            {formatTime(examResult.timeSpent)}
          </Text>
        </View>

        {!examResult.isPassed && (
          <View style={styles.failureReason}>
            <Text style={styles.failureReasonText}>
              {getFailureReasonText()}
            </Text>
          </View>
        )}
      </Card>

      {/* Set Results */}
      <Card style={styles.setResultsCard}>
        <Text style={styles.cardTitle}>各冊成績</Text>

        {Object.entries(examResult.setResults).map(([setKey, setResult]) => (
          <View key={setKey} style={styles.setResult}>
            <View style={styles.setHeader}>
              <Text style={styles.setName}>{setResult.name}</Text>
              <Text style={[
                styles.setScore,
                setResult.wrongCount > 2 ? styles.setScoreFailed : styles.setScorePassed
              ]}>
                {setResult.correctCount}/{setResult.totalQuestions}
              </Text>
            </View>

            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: `${(setResult.correctCount / setResult.totalQuestions) * 100}%` }
                ]}
              />
            </View>

            <Text style={styles.setDetails}>
              正確: {setResult.correctCount} 題 | 錯誤: {setResult.wrongCount} 題
            </Text>
          </View>
        ))}
      </Card>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <Button
          title="查看答題詳情"
          onPress={handleStartReview}
          style={styles.reviewButton}
        />

        <Button
          title="返回首頁"
          onPress={handleGoHome}
          variant="outline"
          style={styles.homeButton}
        />
      </View>
    </View>
  );

  const renderReview = () => {
    const questions = getAllQuestionsForReview();
    const currentItem = questions[currentReviewIndex];

    if (!currentItem) return null;

    return (
      <>
        {/* Fixed Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <TouchableOpacity onPress={() => setReviewMode(false)} className="">
              <Ionicons name="chevron-back" size={28} color="black" />
            </TouchableOpacity>
          </View>

          <View style={styles.headerCenter}>
            <Text style={styles.reviewTitle}>答題詳情</Text>
          </View>

          <View style={styles.headerRight}>
            <TouchableOpacity
              style={styles.toggleButton}
              onPress={() => setShowQuestionNumbers(!showQuestionNumbers)}
            >
              <Text style={styles.toggleButtonText}>
                {showQuestionNumbers ? '隱藏' : '題號'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Question Numbers */}
        {showQuestionNumbers && (
          <QuestionNumbersBar
            ref={questionNumbersScrollRef}
            totalQuestions={questions.length}
            currentIndex={currentReviewIndex}
            questionStates={questions.map(q => q.isCorrect ? 'correct' : 'wrong')}
            onQuestionPress={setCurrentReviewIndex}
            onLayout={(event) => {
              setScrollViewWidth(event.nativeEvent.layout.width);
            }}
            onScroll={handleScroll}
          />
        )}

        <ScrollView style={styles.reviewScrollContainer} showsVerticalScrollIndicator={false}>
          <View style={styles.reviewContent}>
            {/* Question Content */}
            <QuestionCard
              question={currentItem.question}
              questionNumber={currentReviewIndex + 1}
              totalQuestions={questions.length}
            />

            <OptionsList
              options={currentItem.question.options}
              selectedOption={currentItem.userAnswer === -1 ? undefined : currentItem.userAnswer}
              onOptionSelect={() => { }} // Read-only in review mode
              showAnswer={true}
              disabled={true}
              disableShuffling={true} // Disable shuffling in exam result - show same order as during exam
            />
          </View>
        </ScrollView>

        {/* Sticky Bottom Navigation */}
        <View style={styles.stickyBottomContainer}>
          <Button
            title="上一題"
            onPress={() => setCurrentReviewIndex(Math.max(0, currentReviewIndex - 1))}
            disabled={currentReviewIndex <= 0}
            variant="outline"
            style={styles.navButton}
            textStyle={styles.navButtonText}
          />

          <Button
            title="下一題"
            onPress={() => setCurrentReviewIndex(Math.min(questions.length - 1, currentReviewIndex + 1))}
            disabled={currentReviewIndex >= questions.length - 1}
            variant="outline"
            style={styles.navButton}
            textStyle={styles.navButtonText}
          />
        </View>
      </>
    );
  };

  // Handle manual scrolling detection
  const handleScroll = (event: any) => {
    setCurrentScrollX(event.nativeEvent.contentOffset.x);
    setIsManualScrolling(true);

    // Clear existing timeout
    if (manualScrollTimeoutRef.current) {
      clearTimeout(manualScrollTimeoutRef.current);
    }

    // Reset manual scrolling flag after user stops scrolling
    manualScrollTimeoutRef.current = setTimeout(() => {
      setIsManualScrolling(false);
    }, 1000); // Allow 1 second of no scrolling before enabling auto-scroll again
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {reviewMode ? (
        renderReview()
      ) : (
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {renderResult()}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingBottom: 40,
  },
  resultHeader: {
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  resultIcon: {
    fontSize: 60,
    marginBottom: 15,
  },
  resultTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  passedTitle: {
    color: COLORS.SUCCESS,
  },
  failedTitle: {
    color: COLORS.ERROR,
  },
  resultSubtitle: {
    fontSize: 16,
    color: COLORS.TEXT_LIGHT,
    textAlign: 'center',
  },
  summaryCard: {
    marginBottom: 20,
  },
  setResultsCard: {
    marginBottom: 30,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT,
    marginBottom: 20,
    textAlign: 'center',
  },
  scoreRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  scoreLabel: {
    fontSize: 16,
    color: COLORS.TEXT,
  },
  scoreValue: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.THEME_TEXT,
  },
  failureReason: {
    padding: 15,
    borderRadius: 8,
    marginTop: 10,
  },
  failureReasonText: {
    fontSize: 16,
    color: COLORS.ERROR,
    textAlign: 'center',
  },
  setResult: {
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  setHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  setName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT,
  },
  setScore: {
    fontSize: 16,
    fontWeight: '600',
  },
  setScorePassed: {
    color: COLORS.SUCCESS,
  },
  setScoreFailed: {
    color: COLORS.ERROR,
  },
  progressBar: {
    height: 8,
    backgroundColor: COLORS.BORDER,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.SUCCESS,
    borderRadius: 4,
  },
  setDetails: {
    fontSize: 14,
    color: COLORS.TEXT_LIGHT,
  },
  buttonContainer: {
    gap: 15,
  },
  reviewButton: {
    backgroundColor: COLORS.THEME,
  },
  homeButton: {
    borderColor: COLORS.THEME,
    backgroundColor: COLORS.CARD_BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 5,
    backgroundColor: COLORS.BACKGROUND,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  headerLeft: {
    flex: 1,
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 2,
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: COLORS.THEME_TEXT,
    fontWeight: '600',
  },
  reviewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT,
  },
  reviewProgress: {
    fontSize: 14,
    color: COLORS.TEXT_LIGHT,
    fontWeight: '600',
  },
  questionContent: {
    flex: 1,
    marginBottom: 20,
  },
  setTag: {
    alignSelf: 'flex-start',
    backgroundColor: COLORS.THEME + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    marginBottom: 15,
  },
  setTagText: {
    fontSize: 12,
    color: COLORS.THEME_TEXT,
    fontWeight: '600',
  },
  questionCard: {
    marginBottom: 20,
  },
  resultTag: {
    alignSelf: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginTop: 20,
  },
  correctTag: {
    backgroundColor: COLORS.SUCCESS + '20',
  },
  wrongTag: {
    backgroundColor: COLORS.ERROR + '20',
  },
  reviewScrollContainer: {
    flex: 1,
  },
  reviewContent: {
    padding: 20,
    paddingTop: 10,
    paddingBottom: 40,
  },
  stickyBottomContainer: {
    backgroundColor: COLORS.BACKGROUND,
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 0,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  navButton: {
    flex: 1,
    marginHorizontal: 5,
    backgroundColor: COLORS.THEME,
    borderWidth: 0,
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  toggleButton: {
    backgroundColor: COLORS.THEME,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  toggleButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
}); 