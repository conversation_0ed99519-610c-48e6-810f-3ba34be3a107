import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { Al<PERSON>, ScrollView, StyleSheet, Text, View } from 'react-native';

import { <PERSON><PERSON>, Card } from '../../src/components/common';
import { QuestionManager } from '../../src/services/questions/manager';
import { useExamStore } from '../../src/store/useExamStore';
import { COLORS, EXAM_RULES } from '../../src/utils/constants';

export default function ExamEntryScreen() {
  const [isLoading, setIsLoading] = useState(false);
  const { startExam } = useExamStore();

  // Define notes array for dynamic rendering
  const examNotes = [
    '考試過程中不會顯示正確答案',
    '可以隨時切換題目',
    '已作答的題目會保存您的選擇',
    '提交按鈕會在作答所有問題後出現',
    '時間到達後系統會自動提交',
    '未作答的題目將被視為錯誤',   
  ];

  // Define exam rules array for dynamic rendering
  const examRules = [
    {
      title: '題目分配',
      description: `目前總共 ${EXAM_RULES.TOTAL_QUESTIONS} 題（第1-5冊）`,
      content: [
        `第1冊：${EXAM_RULES.QUESTIONS_PER_VOLUME[1]} 題`,
        `第2冊：${EXAM_RULES.QUESTIONS_PER_VOLUME[2]} 題`,
        `第3冊：${EXAM_RULES.QUESTIONS_PER_VOLUME[3]} 題`,
        `第4冊：${EXAM_RULES.QUESTIONS_PER_VOLUME[4]} 題`,
        `第5冊：${EXAM_RULES.QUESTIONS_PER_VOLUME[5]} 題`
      ]
    },
    {
      title: '考試時間',
      content: [`限時 ${EXAM_RULES.TIME_LIMIT_MINUTES} 分鐘`]
    },
    {
      title: '合格條件',
      content: [`錯誤題數不超過 ${EXAM_RULES.MAX_WRONG_TOTAL} 題`,
        `且每冊錯誤題數不超過 ${EXAM_RULES.MAX_WRONG_PER_VOLUME} 題`]
    }
  ];

  const handleStartExam = async () => {
    setIsLoading(true);
    try {
      // Generate exam questions with paper map
      const examData = await QuestionManager.generateExamQuestions();

      // Validate question set
      if (!QuestionManager.validateQuestionSet(examData.questions)) {
        throw new Error('Invalid question set generated');
      }

      // Start the exam
      startExam(examData);
      router.replace('/exam/session');
    } catch (error) {
      console.error('Failed to start exam:', error);
      Alert.alert(
        '錯誤',
        '無法載入考試題目，請檢查您的網路連接或稍後再試。',
        [{ text: '確定' }]
      );
    } finally {
      setIsLoading(false);
    }
  };


  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>澳門駕駛理論考試</Text>
          <Text style={styles.subtitle}>模擬考試</Text>
        </View>

        {/* Exam Rules Card */}
        <Card style={styles.rulesCard}>
          <Text style={styles.cardTitle}>考試規則</Text>
          
          {examRules.map((rule, index) => (
            <View key={index} style={[
              styles.ruleItem,
              index === examRules.length - 1 && styles.lastRuleItem
            ]}>
              <Text style={styles.ruleTitle}>{rule.title}</Text>
              {/* Show description for first rule (題目分配) */}
              {index === 0 && rule.description && (
                <Text style={styles.ruleDescription}>{rule.description}</Text>
              )}
              
              {/* Special layout for first rule item (題目分配) */}
              {index === 0 ? (
                <View style={styles.twoColumnContainer}>
                  <View style={styles.leftColumn}>
                    {rule.content.slice(0, Math.ceil(rule.content.length / 2)).map((item, subIndex) => (
                      <View key={subIndex} style={styles.ruleLineItem}>
                        <Text style={styles.ruleDot}>•</Text>
                        <Text style={styles.ruleText}>
                          {item}
                        </Text>
                      </View>
                    ))}
                  </View>
                  <View style={styles.rightColumn}>
                    {rule.content.slice(Math.ceil(rule.content.length / 2)).map((item, subIndex) => (
                      <View key={subIndex + Math.ceil(rule.content.length / 2)} style={styles.ruleLineItem}>
                        <Text style={styles.ruleDot}>•</Text>
                        <Text style={styles.ruleText}>
                          {item}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>
              ) : (
                <View style={styles.ruleContentList}>
                  {rule.content.map((item, subIndex) => (
                    <View key={subIndex} style={styles.ruleLineItem}>
                      <Text style={styles.ruleDot}>•</Text>
                      <Text style={styles.ruleText}>
                        {item}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          ))}
        </Card>

        {/* Important Notes Card */}
        <Card style={styles.notesCard}>
          <Text style={styles.cardTitle}>重要提醒</Text>
          
          <View style={styles.notesList}>
            {examNotes.map((note, index) => (
              <View key={index} style={styles.noteItem}>
                <Text style={styles.noteDot}>•</Text>
                <Text style={styles.noteText}>
                  {note}
                </Text>
              </View>
            ))}
          </View>
        </Card>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            title="開始考試"
            onPress={handleStartExam}
            loading={isLoading}
            style={styles.startButton}
            textStyle={styles.startButtonText}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingTop: 0,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 15,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.TEXT,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.TEXT_LIGHT,
    textAlign: 'center',
  },
  rulesCard: {
    marginBottom: 20,
    shadowColor: COLORS.SHADOW,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.5,
    shadowRadius: 8,
    elevation: 3,
  },
  notesCard: {
    marginBottom: 20,
    backgroundColor: '#FFF8E1',
    shadowColor: COLORS.SHADOW,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.5,
    shadowRadius: 8,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT,
    marginBottom: 10,
    textAlign: 'center',
  },
  ruleItem: {
    marginBottom: 12,
    paddingLeft: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER + '30',
  },
  lastRuleItem: {
    marginBottom: 0,
    borderBottomWidth: 0,
    paddingBottom: 0,
  },
  ruleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.THEME_TEXT,
    marginBottom: 8,
  },
  ruleDescription: {
    fontSize: 14,
    color: COLORS.SECONDARY_TEXT,
    marginBottom: 12,
    textAlign: 'left',
  },
  ruleContentList: {
    marginBottom: 5,
  },
  ruleLineItem: {
    flexDirection: 'row',
    marginBottom: 0,
    alignItems: 'flex-start',
  },
  ruleDot: {
    fontSize: 16,
    color: COLORS.TEXT,
    marginRight: 10,
  },
  ruleText: {
    fontSize: 14,
    color: COLORS.TEXT,
    lineHeight: 20,
    marginBottom: 3,
  },
  notesList: {
    paddingLeft: 10,
  },
  noteItem: {
    flexDirection: 'row',
    marginBottom: 6,
    alignItems: 'flex-start',
  },
  noteDot: {
    fontSize: 16,
    color: COLORS.WARNING,
    marginRight: 10,
  },
  noteText: {
    flex: 1,
    fontSize: 14,
    color: COLORS.TEXT,
    lineHeight: 20,
  },
  buttonContainer: {
    gap: 15,
  },
  startButton: {
    backgroundColor: COLORS.THEME,
    paddingVertical: 16,
  },
  startButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  twoColumnContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  leftColumn: {
    flex: 1,
    marginRight: 10, // Space between columns
  },
  rightColumn: {
    flex: 1,
  },
}); 