import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useRef, useState } from 'react';
import { Alert, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Button } from '../../src/components/common';
import { OptionsList, QuestionCard, QuestionNumbersBar } from '../../src/components/question';
import { useRealtimeAnswer } from '../../src/hooks/useRealtimeAnswer';
import { useExamStore } from '../../src/store/useExamStore';
import { COLORS, DEBUG } from '../../src/utils/constants';

export default function ExamSessionScreen() {
  const [showQuestionNumbers, setShowQuestionNumbers] = useState(false);
  const [scrollViewWidth, setScrollViewWidth] = useState(0);
  const [currentScrollX, setCurrentScrollX] = useState(0);
  const [isManualScrolling, setIsManualScrolling] = useState(false);
  const questionNumbersScrollRef = useRef<ScrollView>(null);
  const manualScrollTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const {
    session,
    currentQuestion,
    selectedAnswer,
    stats,
    isActive,
    isAutoSubmitted,
    selectAnswer,
    nextQuestion,
    previousQuestion,
    jumpToQuestion,
    submitExam,
    reset, // Add reset function
  } = useExamStore();

  const { recordAnswer } = useRealtimeAnswer();
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!session && !isSubmitting) {
      router.dismissTo('/');
    }
  }, [session, isSubmitting]);

  // Handle auto-submission navigation
  useEffect(() => {
    if (isAutoSubmitted && !isActive && session?.endTime) {
      console.log('Exam auto-submitted due to time limit');
      router.replace('/exam/result');
    }
  }, [isAutoSubmitted, isActive, session?.endTime]);

  // Cleanup timer when component unmounts
  useEffect(() => {
    return () => {
      // If user navigates away while exam is active, clean up
      const examStore = useExamStore.getState();
      if (examStore.isActive && examStore.timerId) {
        reset(); // This will clear the timer and reset the state
      }
    };
  }, [reset]);

  // Auto-scroll to current question in question numbers bar (intelligent scrolling)
  useEffect(() => {
    if (showQuestionNumbers && questionNumbersScrollRef.current && session && scrollViewWidth > 0 && !isManualScrolling) {
      const questionWidth = 36; // Width of each question number button
      const questionGap = 8; // Gap between buttons
      const itemWidth = questionWidth + questionGap;
      const currentQuestionPosition = session.currentIndex * itemWidth;
      
      // Calculate visible area with buffer
      const buffer = itemWidth * 1.5; // Show 1.5 questions buffer on each side
      const visibleStart = currentScrollX;
      const visibleEnd = currentScrollX + scrollViewWidth;
      
      // Check if current question is outside visible area (with buffer)
      const questionStart = currentQuestionPosition;
      const questionEnd = currentQuestionPosition + questionWidth;
      
      const needsScrolling = questionStart < visibleStart + buffer || questionEnd > visibleEnd - buffer;
      
      if (needsScrolling) {
        // Center the current question in the viewport
        const scrollPosition = Math.max(0, currentQuestionPosition - (scrollViewWidth / 2) + (questionWidth / 2));
        
        setTimeout(() => {
          questionNumbersScrollRef.current?.scrollTo({
            x: scrollPosition,
            animated: true,
          });
        }, 100);
      }
    }
  }, [session?.currentIndex, showQuestionNumbers, scrollViewWidth, currentScrollX, isManualScrolling, session]);

  // Handle manual scrolling detection
  const handleScroll = (event: any) => {
    setCurrentScrollX(event.nativeEvent.contentOffset.x);
    setIsManualScrolling(true);
    
    // Clear existing timeout
    if (manualScrollTimeoutRef.current) {
      clearTimeout(manualScrollTimeoutRef.current);
    }
    
    // Reset manual scrolling flag after user stops scrolling
    manualScrollTimeoutRef.current = setTimeout(() => {
      setIsManualScrolling(false);
    }, 1000); // Allow 1 second of no scrolling before enabling auto-scroll again
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (manualScrollTimeoutRef.current) {
        clearTimeout(manualScrollTimeoutRef.current);
      }
    };
  }, []);

  if (!session || !currentQuestion) {
    return null;
  }

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = (answerIndex: number) => {
    selectAnswer(answerIndex);
  };

  const handleSubmitExam = async () => {
    try {
      setIsSubmitting(true);
      const submitStartTime = Date.now();
      submitExam();
      
      // Record all answers to database sequentially to avoid transaction conflicts
      for (let index = 0; index < session.questions.length; index++) {
        const question = session.questions[index];
        const userAnswer = session.answers[index]; // This is the original answer index

        if (userAnswer !== undefined) {
          // Find the correct option in the shuffled options
          const correctShuffledIndex = question.options.findIndex(opt => opt.isCorrect);
          const correctOptionLabel = ['A', 'B', 'C', 'D'][correctShuffledIndex];

          // Check if answer is correct using the original answer index
          // We need to check correctness against the original options, not the shuffled display
          let isCorrect = false;
          let selectedOptionLabel = 'A';

          if (correctShuffledIndex !== -1) {
            const optionMap = session.paperMap.optionMaps[index];
            const correctOriginalIndex = optionMap ? optionMap.displayToOriginal[correctShuffledIndex] : correctShuffledIndex;
            isCorrect = userAnswer === correctOriginalIndex;

            // Convert original answer index to displayed answer index for label
            const displayAnswerIndex = optionMap ? optionMap.originalToDisplay[userAnswer] : userAnswer;
            selectedOptionLabel = ['A', 'B', 'C', 'D'][displayAnswerIndex];
          }

          try {
            await recordAnswer({
              questionId: question.id,
              volume: question.volume,
              chapter: 1, // Default chapter
              isCorrect: isCorrect,
              mode: 'exam',
              sessionId: session.id,
              selectedOption: selectedOptionLabel as 'A' | 'B' | 'C' | 'D',
              correctOption: correctOptionLabel as 'A' | 'B' | 'C' | 'D',
              timeSpent: 0, // Individual time tracking not implemented for exam
            });
          } catch (answerError) {
            console.error(`Failed to record answer for question ${index + 1}:`, answerError);
            // Continue with other answers even if one fails
          }
        }
      }
      // Ensure at least 1s wait before navigating to result
      const elapsedMs = Date.now() - submitStartTime;
      if (elapsedMs < 1000) {
        await new Promise(resolve => setTimeout(resolve, 1000 - elapsedMs));
      }

      router.replace('/exam/result');
    } catch (error) {
      console.error('Failed to submit exam:', error);
      Alert.alert(
        '提交失敗',
        '無法提交考試結果，請稍後再試。',
        [{ text: '確定' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderTimer = () => (
    <View style={[styles.timerContainer, stats.timeRemaining <= 300 && styles.timerWarning]}>
      <View style={styles.timerContent}>
        <MaterialIcons 
          name="schedule" 
          size={16} 
          color={stats.timeRemaining <= 300 ? COLORS.ERROR : COLORS.SECONDARY_TEXT} 
        />
        <Text style={[styles.timerText, stats.timeRemaining <= 300 && styles.timerTextWarning]}>
          {formatTime(stats.timeRemaining)}
        </Text>
      </View>
    </View>
  );

  const renderProgress = () => (
    <View style={styles.progressContainer}>
      <Text style={styles.progressText}>
        已答題: {stats.answeredCount}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      
      {/* Header */}
      <View style={styles.header}>
        {renderTimer()}
        {renderProgress()}
        <TouchableOpacity
          style={styles.toggleButton}
          onPress={() => setShowQuestionNumbers(!showQuestionNumbers)}
        >
          <Text style={styles.toggleButtonText}>
            {showQuestionNumbers ? '隱藏' : '題號'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Debug Mode Indicator */}
      {DEBUG.SHOW_CORRECT_ANSWER_IN_EXAM && (
        <View style={styles.debugIndicator}>
          <Text style={styles.debugText}>🐛 DEBUG MODE - 正確答案已顯示</Text>
        </View>
      )}

      {/* Question Numbers */}
      {showQuestionNumbers && <QuestionNumbersBar
        ref={questionNumbersScrollRef}
        totalQuestions={session.questions.length}
        currentIndex={session.currentIndex}
        questionStates={session.questions.map((_, index) => 
          session.answers[index] !== undefined ? 'answered' : 'unanswered'
        )}
        onQuestionPress={jumpToQuestion}
        onLayout={(event) => {
          setScrollViewWidth(event.nativeEvent.layout.width);
        }}
        onScroll={handleScroll}
      />}

      {/* Main Content */}
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <QuestionCard
          question={currentQuestion}
          questionNumber={session.currentIndex + 1}
          totalQuestions={session.questions.length}
        />

        <OptionsList
          options={currentQuestion.options}
          selectedOption={selectedAnswer ?? undefined}
          onOptionSelect={handleAnswerSelect}
          showAnswer={DEBUG.SHOW_CORRECT_ANSWER_IN_EXAM} // Show correct answer in debug mode
          disabled={isSubmitting}
          disableShuffling={true} // Disable shuffling in exam mode - options are already shuffled in question generation
        />
      </ScrollView>
        
      {/* Sticky Bottom Navigation */}
      <View style={styles.stickyBottomContainer}>
        <View style={styles.navigationButtons}>
          <Button
            title="上一題"
            onPress={previousQuestion}
            disabled={session.currentIndex <= 0 || isSubmitting}
            variant="outline"
            style={styles.navButton}
            textStyle={styles.navButtonText}
          />
          
          <Button
            title="下一題"
            onPress={nextQuestion}
            disabled={session.currentIndex >= session.questions.length - 1 || isSubmitting}
            variant="outline"
            style={styles.navButton}
            textStyle={styles.navButtonText}
          />
        </View>
        
        {/* Only show submit button when all questions are answered */}
        {stats.answeredCount !== session.questions.length && (
          <Button
            title="提交考試"
            onPress={handleSubmitExam}
            loading={isSubmitting}
            disabled={isSubmitting}
            style={styles.submitButton}
            textStyle={styles.submitButtonText}
          />
        )}
      </View>
      {isSubmitting && (
        <View style={styles.submittingOverlay}>
          <Text style={styles.submittingText}>正在提交，請稍候…</Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.BACKGROUND,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  timerContainer: {
    // backgroundColor: COLORS.THEME + '20',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    width: 100,
    textAlign: 'center',
  },
  timerWarning: {
    // backgroundColor: COLORS.ERROR + '20',
  },
  timerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  timerText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT,
  },
  timerTextWarning: {
    color: COLORS.ERROR,
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT,
  },

  toggleButton: {
    backgroundColor: COLORS.THEME,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  toggleButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  scrollContainer: {
    flex: 1,
    paddingTop: 10,
    padding: 20,
  },
  stickyBottomContainer: {
    backgroundColor: COLORS.BACKGROUND,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    padding: 20,
    paddingTop: 10,
    paddingBottom: 0,
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    gap: 10,
  },
  navButton: {
    flex: 1,
    backgroundColor: COLORS.THEME,
    borderWidth: 0,
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  submitButton: {
    backgroundColor: COLORS.THEME,
    paddingVertical: 16,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  debugIndicator: {
    backgroundColor: COLORS.WARNING,
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignItems: 'center',
  },
  debugText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  submittingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  submittingText: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 12,
    backgroundColor: COLORS.CARD_BACKGROUND,
    color: COLORS.TEXT,
    fontSize: 16,
    fontWeight: '600',
  },
}); 