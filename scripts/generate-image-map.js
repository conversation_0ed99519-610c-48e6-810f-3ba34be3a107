// Generates a static mapping from logical image paths in JSON (e.g. "volume1/1.png")
// to require(...) calls that React Native can bundle at build time.
//
// Output: src/services/questions/imageMap.ts
//
// Usage: node scripts/generate-image-map.js

const fs = require('fs');
const path = require('path');

const projectRoot = path.resolve(__dirname, '..');
const assetsDir = path.resolve(projectRoot, 'assets', 'data', 'questions');
const outFile = path.resolve(projectRoot, 'src', 'services', 'questions', 'imageMap.ts');

/**
 * Collect png files inside questions/volumeX directories and build a mapping
 * key: "volumeX/<file>"
 * value: require('.../assets/data/questions/volumeX/<file>')
 */
function buildMap() {
  const mapEntries = [];
  const volumes = fs.readdirSync(assetsDir).filter((name) => fs.statSync(path.join(assetsDir, name)).isDirectory());

  for (const vol of volumes) {
    const volDir = path.join(assetsDir, vol);
    const files = fs
      .readdirSync(volDir)
      .filter((f) => f.toLowerCase().endsWith('.png'))
      .sort((a, b) => {
        // numeric-first sort if file names are numbers like 1.png, 2.png
        const an = parseInt(a, 10);
        const bn = parseInt(b, 10);
        if (!Number.isNaN(an) && !Number.isNaN(bn)) return an - bn;
        return a.localeCompare(b, 'en');
      });

    for (const f of files) {
      const key = `${vol}/${f}`.replace(/\\/g, '/');
      const relRequirePath = path
        .relative(path.dirname(outFile), path.join(assetsDir, vol, f))
        .replace(/\\/g, '/');
      mapEntries.push({ key, rel: relRequirePath.startsWith('.') ? relRequirePath : `./${relRequirePath}` });
    }
  }

  const header = `// AUTO-GENERATED FILE. Do not edit manually.\n` +
    `// Run: node scripts/generate-image-map.js\n` +
    `// Maps logical image paths from JSON (e.g. "volume1/1.png") to static require(...) so RN can bundle images.\n\n`;

  const records = mapEntries
    .map(({ key, rel }) => `  ${JSON.stringify(key)}: require(${JSON.stringify(rel)}),`)
    .join('\n');

  const content = `${header}export const IMAGE_MAP = {\n${records}\n} as const;\n\nexport type ImageKey = keyof typeof IMAGE_MAP;\n`;

  fs.mkdirSync(path.dirname(outFile), { recursive: true });
  fs.writeFileSync(outFile, content, 'utf8');
  console.log(`Generated ${outFile} with ${mapEntries.length} entries.`);
}

buildMap();


