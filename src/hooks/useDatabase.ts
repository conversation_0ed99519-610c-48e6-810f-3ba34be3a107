import { useState, useEffect, useCallback } from 'react';
import { initializeDatabase } from '../services/database';
import * as SQLite from 'expo-sqlite';

export function useDatabase() {
  const [database, setDatabase] = useState<SQLite.SQLiteDatabase | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initialize = useCallback(async () => {
    try {
      setError(null);
      const db = await initializeDatabase();
      setDatabase(db);
      setIsInitialized(true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize database';
      setError(errorMessage);
      console.error('Database initialization error:', err);
    }
  }, []);

  useEffect(() => {
    initialize();
  }, [initialize]);

  return {
    database,
    isInitialized,
    error,
    reinitialize: initialize,
  };
}