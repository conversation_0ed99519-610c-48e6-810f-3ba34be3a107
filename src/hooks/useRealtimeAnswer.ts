import { useCallback } from 'react';
import { AnswerData, recordAnswer } from '../services/database';
import { useAppStore } from '../store/useAppStore';

export function useRealtimeAnswer() {
  const { setError } = useAppStore();

  const recordAnswerRealtime = useCallback(async (answerData: AnswerData) => {
    try {
      await recordAnswer(answerData);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to record answer';
      setError(errorMessage);
      console.error('Failed to record answer:', error);
      throw error;
    }
  }, [setError]);

  return { recordAnswer: recordAnswerRealtime };
}