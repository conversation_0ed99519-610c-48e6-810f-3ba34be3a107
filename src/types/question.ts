export interface Question {
  id: number;
  volume?: number; // Will be added during loading
  chapter?: number; // Will be added during loading
  question: string;
  image_url?: string;
  options: {
    text: string;
    isCorrect: boolean;
  }[];
  tags?: string[];
}

export interface ProcessedQuestion {
  id: number;
  volume: number;
  question: string;
  options: {
    text: string;
    isCorrect: boolean;
  }[];
  explanation?: string;
  image?: string;
  tags?: string[];
}

export interface QuestionSession {
  id: string;
  type: 'practice' | 'exam';
  title: string;
  questions: ProcessedQuestion[];
  currentIndex: number;
  answers: Record<number, number>;
  startTime: Date;
  endTime?: Date;
}