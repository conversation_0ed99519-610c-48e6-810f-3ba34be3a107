export interface VolumeStats {
  volume: number;
  totalQuestions: number;
  seenQuestions: number;
  correctAnswers: number;
  wrongAnswers: number;
  accuracy: number;
  lastPracticeDate?: Date;
}

export interface OverallStats {
  totalQuestionsAnswered: number;
  correctAnswers: number;
  wrongAnswers: number;
  accuracy: number;
  streakCount: number;
  totalTimeSpent: number;
  averageTimePerQuestion: number;
  volumeStats: VolumeStats[];
}

export interface RecentActivity {
  date: string;
  sessionsCount: number;
  questionsAnswered: number;
  accuracy: number;
}

export interface PredictedPassRate {
  percentage: number;
  confidence: 'low' | 'medium' | 'high';
  recommendations: string[];
}