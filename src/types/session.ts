import { ProcessedQuestion } from './question';

export interface PracticeSession {
  id: string;
  title: string;
  questions: ProcessedQuestion[];
  currentIndex: number;
  answers: Record<number, number>;
  startTime: Date;
  endTime?: Date;
  volumes: number[];
  config: PracticeConfig;
}

export interface PracticeConfig {
  volumes: number[]; // Changed from single volume to array of volumes
  chapter: number | null;
  mode: 'sequential' | 'random';
  includeWrongQuestions: boolean;
  includeBookmarked: boolean;
  includeUnseen: boolean; // New option to filter unseen questions
}

// Exam paper mapping to preserve original question and option order
export interface ExamPaperMap {
  // Maps exam question index to original question info
  questionMap: {
    [examIndex: number]: {
      originalQuestionId: number;
      originalVolume: number;
      setNumber: number; // Which set this question belongs to (1-5)
      setIndex: number;  // Index within the set (0-based)
    };
  };
  // Maps exam question index to option shuffling info
  optionMaps: {
    [examIndex: number]: {
      // Maps displayed option index to original option index
      displayToOriginal: number[];
      // Maps original option index to displayed option index
      originalToDisplay: number[];
    };
  };
  // Set boundaries for easy navigation
  setBoundaries: {
    set1: { start: number; end: number; count: number };
    set2: { start: number; end: number; count: number };
    set3: { start: number; end: number; count: number };
    set4: { start: number; end: number; count: number };
    set5: { start: number; end: number; count: number };
  };
}

export interface ExamSession {
  id: string;
  title: string;
  questions: ProcessedQuestion[];
  currentIndex: number;
  answers: Record<number, number>; // Maps exam question index to ORIGINAL option index
  startTime: Date;
  endTime?: Date;
  timeLimit: number; // in minutes
  config: ExamConfig;
  paperMap: ExamPaperMap; // Add the exam paper mapping
}

export interface ExamConfig {
  totalQuestions: number;
  timeLimitMinutes: number;
  questionsPerSet: {
    set1: number; // Volume 1
    set2: number; // Volume 2
    set3: number; // Volume 3
    set4: number; // Volume 4
    set5: number; // Volume 5
  };
  maxWrongTotal: number;
  maxWrongPerSet: number;
}

export interface ExamResult {
  sessionId: string;
  isPassed: boolean;
  score: number;
  totalQuestions: number;
  correctCount: number;
  wrongCount: number;
  timeSpent: number; // in seconds
  failureReason?: 'too_many_wrong_total' | 'too_many_wrong_per_set';
  setResults: {
    set1: ExamSetResult;
    set2: ExamSetResult;
    set3: ExamSetResult;
    set4: ExamSetResult;
    set5: ExamSetResult;
  };
}

export interface ExamSetResult {
  name: string;
  volumes: number[];
  totalQuestions: number;
  correctCount: number;
  wrongCount: number;
  questions: ProcessedQuestion[];
  userAnswers: number[];
}

export interface SessionStats {
  totalQuestions: number;
  answeredCount: number;
  correctCount: number;
  wrongCount: number;
  accuracy: number;
  timeSpent: number;
}

export interface ExamStats extends SessionStats {
  timeRemaining: number;
  currentSet: number;
  setProgress: {
    set1: { answered: number; total: number };
    set2: { answered: number; total: number };
    set3: { answered: number; total: number };
    set4: { answered: number; total: number };
    set5: { answered: number; total: number };
  };
}