import { ProcessedQuestion } from '../types/question';

/**
 * Detects if a question contains cross-references between options that would break if shuffled
 * Keywords that indicate cross-referencing:
 * - Direct references: "A和B", "A和C", "A、B和C"
 * - References with 項 (item): "A項和B項", "上述A項和C項"
 * - References with 上述 (above mentioned): "上述A、B及C項"
 * - Method references: "按上述A、B及C項的方法處理"
 */
export function shouldNotShuffle(options: ProcessedQuestion['options']): boolean {
  // Regex patterns to detect cross-references
  const crossReferencePatterns = [
    // Direct letter combinations with conjunctions
    /[A-D][和、及][A-D]/,  // A和B, A、B, A及B
    /[A-D]、[A-D][和及][A-D]/, // A、B和C, A、B及C
    
    // References with 項 (item)
    /[A-D]項[和及][A-D]項/, // A項和B項, A項及B項
    
    // References with 上述 (above mentioned)
    /上述[A-D]/, // 上述A, 上述B, etc.
    /上述[A-D]、[A-D]/, // 上述A、B
    /上述[A-D]、[A-D][和及][A-D]/, // 上述A、B和C, 上述A、B及C
    /上述[A-D]項[和及][A-D]項/, // 上述A項和C項
    
    // Method processing references
    /按上述[A-D]/, // 按上述A、B及C項的方法處理
    
    // All combinations patterns
    /[A-D]、[A-D][和及][A-D]所述/, // A、B和C所述情況
    /[A-D]、[A-D][和及][A-D]項/, // A、B及C項
  ];

  // Check if any option contains cross-reference patterns
  return options.some(option => 
    crossReferencePatterns.some(pattern => 
      pattern.test(option.text)
    )
  );
}

/**
 * Shuffles an array using Fisher-Yates algorithm
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * Creates a shuffled version of question options with mapping information
 * Skips shuffling if the question contains cross-references between options
 */
export function createShuffledOptions(options: ProcessedQuestion['options']) {
  // Don't shuffle if the question contains cross-references
  if (shouldNotShuffle(options)) {
    // Return original options with identity mappings
    return {
      shuffledOptions: options,
      mapOriginalToShuffled: (originalIndex: number) => originalIndex,
      mapShuffledToOriginal: (shuffledIndex: number) => shuffledIndex,
    };
  }

  // Create array of options with their original indices
  const optionsWithIndices = options.map((option, index) => ({
    ...option,
    originalIndex: index,
  }));

  // Shuffle the options
  const shuffledOptions = shuffleArray(optionsWithIndices);

  // Create mapping from new index to original index
  const indexMapping = shuffledOptions.map(option => option.originalIndex);

  // Create mapping from original index to new index  
  const reverseIndexMapping = new Map<number, number>();
  indexMapping.forEach((originalIndex, newIndex) => {
    reverseIndexMapping.set(originalIndex, newIndex);
  });

  return {
    shuffledOptions: shuffledOptions.map(({ originalIndex, ...option }) => option),
    mapOriginalToShuffled: (originalIndex: number) => reverseIndexMapping.get(originalIndex) ?? originalIndex,
    mapShuffledToOriginal: (shuffledIndex: number) => indexMapping[shuffledIndex] ?? shuffledIndex,
  };
} 