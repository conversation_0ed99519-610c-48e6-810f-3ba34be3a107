export const DEBUG = {
  SHOW_CORRECT_ANSWER_IN_EXAM: false, // Set to false for production
  SHOW_SUBMIT_BUTTON_FOR_TESTING: true, // Set to true to always show submit button for testing
} as const;

export const VOLUMES = {
  TOTAL: 5, // Now 5 volumes available
  NAMES: {
    1: '道路交通標誌',
    2: '道路交通標線',
    3: '道路交通燈號',
    4: '交通指揮手勢',
    5: '駕駛行為與安全',
  },
  COUNTS: {
    1: 176,
    2: 66,
    3: 150,
    4: 139,
    5: 116,
  },
} as const;

export const PRACTICE_CONFIG = {
  DEFAULT_QUESTION_COUNT: 20,
  MAX_QUESTION_COUNT: 100,
  MIN_QUESTION_COUNT: 5,
  MODES: {
    SEQUENTIAL: 'sequential',
    RANDOM: 'random',
  },
} as const;

export const EXAM_RULES = {
  TOTAL_QUESTIONS: 50,
  TIME_LIMIT_MINUTES: 60,
  PASSING_SCORE: 22, // Pass if 8 or fewer wrong (30 - 8 = 22)
  MAX_WRONG_TOTAL: 8, // Fail if more than 8 questions wrong
  MAX_WRONG_PER_VOLUME: 2, // Fail if more than 2 questions wrong in same volume/set
  QUESTIONS_PER_VOLUME: {
    1: 12, // Set 1: 12 questions from volume 1
    2: 8,  // Set 2: 8 questions from volume 2  
    3: 10, // Set 3: 10 questions from volume 3
    4: 10, // Set 4: 10 questions from volume 4
    5: 10, // Set 5: 10 questions from volume 5
  },
} as const;

export const COLORS = {
  THEME: 'rgba(163, 196, 201, 1)',
  THEME_TEXT: 'rgb(68, 169, 184)',
  THEME_DISABLED: 'rgb(163, 163, 163)',
  PRIMARY: 'rgb(177, 197, 218)',
  SUCCESS: 'rgba(52, 199, 89, 1)',
  ERROR: 'rgba(255, 59, 48, 1)',
  WARNING: 'rgba(255, 149, 0, 1)',
  BACKGROUND: 'rgb(241, 237, 229)', // Cream/beige background
  CARD_BACKGROUND: 'rgba(255, 255, 255, 1)',
  CARD_BACKGROUND_DISABLED: 'rgba(140, 140, 140, 0.1)',
  ACCENT: 'rgba(163, 196, 201, 1)', // Blue-green accent color
  ACCENT_DARK: 'rgba(123, 165, 171, 1)',
  TEXT: 'rgba(44, 44, 46, 1)',
  TEXT_LIGHT: 'rgba(109, 109, 112, 1)',
  SECONDARY_TEXT: 'rgba(142, 142, 147, 1)',
  BORDER: 'rgba(229, 229, 234, 1)',
  SHADOW: 'rgba(0, 0, 0, 0.2)',
} as const;

export const FONTS = {
  SIZES: {
    SMALL: {
      title: 20,
      body: 14,
      caption: 12,
    },
    MEDIUM: {
      title: 24,
      body: 16,
      caption: 14,
    },
    LARGE: {
      title: 28,
      body: 18,
      caption: 16,
    },
  },
} as const;

export const TIMING = {
  AUTO_NEXT_DELAY: 2000, // 2 seconds
  ANSWER_FEEDBACK_DELAY: 1000, // 1 second
  LOADING_TIMEOUT: 10000, // 10 seconds
} as const;
