import { COLORS } from '@/src/utils/constants';
import React from 'react';
import {
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { ProcessedQuestion } from '../../types/question';
import { Card } from '../common';
import { QuestionImage } from './QuestionImage';

interface QuestionCardProps {
  question: ProcessedQuestion;
  questionNumber: number;
  totalQuestions?: number;
}

export function QuestionCard({
  question,
  questionNumber,
  totalQuestions,
}: QuestionCardProps) {
  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.questionNumber}>
          第 {questionNumber} 題{totalQuestions ? ` / 共 ${totalQuestions} 題` : ''}
        </Text>
        <Text style={styles.volumeInfo}>
          第 {question.volume} 冊
        </Text>
      </View>

      {question.image && (
        <QuestionImage 
          source={question.image} 
          style={styles.image}
        />
      )}

      <View style={styles.content}>
        <Text style={styles.questionText}>
          {question.question}
        </Text>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  questionNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.THEME_TEXT,
  },
  volumeInfo: {
    fontSize: 14,
    color: '#8E8E93',
  },
  image: {
    marginBottom: 8,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: 10,
    padding: 10
  },
  content: {
    paddingTop: 8,
  },
  questionText: {
    fontSize: 18,
    lineHeight: 24,
    color: '#1c1c1e',
    fontWeight: '500',
  },
});