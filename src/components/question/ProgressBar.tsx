import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';

interface ProgressBarProps {
  current: number;
  total: number;
  showNumbers?: boolean;
  color?: string;
}

export function ProgressBar({
  current,
  total,
  showNumbers = true,
  color = '#007AFF',
}: ProgressBarProps) {
  const progress = Math.min(current / total, 1);

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: withTiming(`${progress * 100}%`, {
        duration: 300,
      }),
    };
  });

  return (
    <View style={styles.container}>
      {showNumbers && (
        <View style={styles.textContainer}>
          <Text style={styles.progressText}>
            進度：{current} / {total}
          </Text>
          <Text style={styles.percentageText}>
            {Math.round(progress * 100)}%
          </Text>
        </View>
      )}
      
      <View style={styles.progressBarContainer}>
        <View style={[styles.progressBarBg]}>
          <Animated.View
            style={[
              styles.progressBarFill,
              { backgroundColor: color },
              progressStyle,
            ]}
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    color: '#8E8E93',
  },
  percentageText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1c1c1e',
  },
  progressBarContainer: {
    width: '100%',
  },
  progressBarBg: {
    height: 8,
    backgroundColor: '#E5E5EA',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
});