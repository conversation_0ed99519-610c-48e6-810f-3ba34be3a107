import React from 'react';
import {
  Image,
  StyleSheet,
  View,
  ViewStyle,
} from 'react-native';
import { IMAGE_MAP, ImageKey } from '../../services/questions/imageMap';

interface QuestionImageProps {
  source: string;
  style?: ViewStyle;
}

export function QuestionImage({ source, style }: QuestionImageProps) {
  const resolvedSource = (IMAGE_MAP as Record<string, number>)[source as ImageKey] ?? { uri: source };
  return (
    <View style={[styles.container, style]}>
      <Image
        source={resolvedSource}
        style={styles.image}
        resizeMode="contain"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 200,
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
});