import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Alert, Pressable, StyleSheet, Text, View } from 'react-native';
import { QuestionManager } from '../../services/questions/manager';
import { usePracticeStore } from '../../store/usePracticeStore';
import { PracticeConfig } from '../../types/session';
import { COLORS } from '../../utils/constants';

type VolumeStatsDisplay = {
  volume: number;
  title: string;
  correct: number;
  wrong: number;
  unseen: number;
  total: number;
};

type VolumePracticeCardProps = {
  volumeStats: VolumeStatsDisplay;
};

const VolumePracticeCard: React.FC<VolumePracticeCardProps> = ({ volumeStats }) => {
  const router = useRouter();
  const { startSession } = usePracticeStore();
  const [loading, setLoading] = useState(false);
  
  const isDisabled = volumeStats.volume < 1;
  const isCompleted = volumeStats.correct >= volumeStats.total;

  const handlePress = async () => {
    if (isDisabled) {
      Alert.alert('即將推出', '此冊別練習功能即將推出，敬請期待！');
      return;
    }

    setLoading(true);
    
    try {
      // Check if there are wrong questions to practice
      const hasWrongQuestions = volumeStats.wrong > 0;
      const hasUnseenQuestions = volumeStats.unseen > 0;
      
      const config: PracticeConfig = {
        volumes: [volumeStats.volume],
        chapter: null,
        mode: 'random',
        includeWrongQuestions: hasWrongQuestions,
        includeBookmarked: false,
        includeUnseen: hasUnseenQuestions, // Always include unseen questions if available
      };

      const questions = await QuestionManager.generatePracticeQuestions(config);
      
      if (questions.length === 0) {
        Alert.alert('沒有題目', '此冊別內沒有找到題目，請稍後再試。');
        return;
      }

      // Check if all questions have been mastered (answered correctly)
      const totalQuestions = volumeStats.total;
      const masteredQuestions = volumeStats.correct;
      const allMastered = masteredQuestions >= totalQuestions;
      
      if (allMastered) {
        Alert.alert(
          '恭喜完成！', 
          '您已掌握此冊的所有題目！現在將開始複習模式，您可以重新練習所有題目來保持熟練度。',
          [{ text: '開始複習', onPress: async () => {
            await startSession(config, questions);
            router.push('/practice/session');
          }}]
        );
        return;
      }

      // Show practice mode info
      if (hasWrongQuestions) {
        console.log(`Starting practice with ${volumeStats.wrong} wrong questions for volume ${volumeStats.volume}`);
      } else if (hasUnseenQuestions) {
        console.log(`Starting practice with ${volumeStats.unseen} unseen questions for volume ${volumeStats.volume}`);
      }

      await startSession(config, questions);
      router.push('/practice/session');
    } catch (error) {
      console.error('Failed to start volume practice:', error);
      Alert.alert('錯誤', '無法載入練習題目，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  const progressData = [
    { value: volumeStats.correct, color: COLORS.SUCCESS }, // Green for correct
    { value: volumeStats.wrong, color: COLORS.ERROR },     // Red for wrong
    { value: volumeStats.unseen, color: COLORS.BORDER },   // Gray for unseen
  ];

  return (
    <Pressable onPress={handlePress} disabled={loading}>
      <View
        style={[
          styles.card, 
          loading && styles.cardLoading,
          isDisabled && styles.cardDisabled
        ]}
      >
        <View style={[styles.overlay, isDisabled && styles.overlayDisabled]}>
          <View style={styles.contentContainer}>
            <Text style={[styles.subHeader, isDisabled && styles.textDisabled]}>
              第 {volumeStats.volume} 冊
            </Text>
            <Text style={[styles.title, isDisabled && styles.textDisabled]}>
              {volumeStats.title}
            </Text>
            {isDisabled ? (
              <Text style={styles.comingSoonText}>即將推出</Text>
            ) : isCompleted ? (
              <Text style={styles.completedText}>✓ 已掌握</Text>
            ) : null}
            {!isDisabled && (
              <View style={styles.progressBarContainer}>
                {progressData.map((segment, index) => {
                  const segmentWidth = (segment.value / volumeStats.total) * 100;
                  if (segmentWidth === 0) return null;
                  return (
                    <View
                      key={index}
                      style={{
                        width: `${segmentWidth}%`,
                        height: '100%',
                        backgroundColor: segment.color,
                      }}
                    />
                  );
                })}
              </View>
            )}
          </View>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  card: {
    // height: 200,
    minHeight: 140,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: COLORS.SHADOW,
    overflow: 'hidden',
    justifyContent: 'flex-end',
    shadowColor: COLORS.SHADOW,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    marginBottom: 4,
    backgroundColor: COLORS.CARD_BACKGROUND,
  },
  cardLoading: {
    opacity: 0.7,
  },
  cardDisabled: {
    opacity: 0.6,
    backgroundColor: COLORS.BORDER,
    borderColor: COLORS.BORDER,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(228, 228, 228, 0.2)',
    padding: 18,
    justifyContent: 'flex-end',
    borderRadius: 16,
  },
  overlayDisabled: {
    backgroundColor: COLORS.CARD_BACKGROUND_DISABLED,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  subHeader: {
    color: COLORS.THEME_TEXT,
    fontSize: 13,
    fontWeight: '700',
    opacity: 0.9,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  title: {
    color: COLORS.TEXT,
    fontSize: 17,
    fontWeight: 'bold',
    marginTop: 6,
    lineHeight: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    overflow: 'hidden',
    flexDirection: 'row',
    marginTop: 12,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.5,
    shadowRadius: 2,
  },
  textDisabled: {
    opacity: 0.7,
  },
  comingSoonText: {
    color: COLORS.WARNING,
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  completedText: {
    color: COLORS.SUCCESS,
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
  },
});

export default VolumePracticeCard;
