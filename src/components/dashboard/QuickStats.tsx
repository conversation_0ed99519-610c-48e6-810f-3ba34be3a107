import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card } from '../common';
import { COLORS } from '../../utils/constants';
import { OverallStats } from '../../types/statistics';

interface QuickStatsProps {
  stats: OverallStats;
}

export function QuickStats({ stats }: QuickStatsProps) {
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}小時${minutes}分鐘`;
    }
    return `${minutes}分鐘`;
  };

  const statsData = [
    {
      label: '總答題數',
      value: stats.totalQuestionsAnswered.toString(),
      color: COLORS.PRIMARY,
    },
    {
      label: '整體正確率',
      value: `${stats.accuracy.toFixed(1)}%`,
      color: stats.accuracy >= 80 ? COLORS.SUCCESS : stats.accuracy >= 60 ? COLORS.WARNING : COLORS.ERROR,
    },
    {
      label: '連續答對',
      value: stats.streakCount.toString(),
      color: COLORS.SUCCESS,
    },
    {
      label: '學習時間',
      value: formatTime(stats.totalTimeSpent),
      color: COLORS.PRIMARY,
    },
  ];

  return (
    <Card style={styles.container}>
      <Text style={styles.title}>學習統計</Text>
      
      <View style={styles.grid}>
        {statsData.map((stat, index) => (
          <View key={index} style={styles.statItem}>
            <Text style={[styles.statValue, { color: stat.color }]}>
              {stat.value}
            </Text>
            <Text style={styles.statLabel}>{stat.label}</Text>
          </View>
        ))}
      </View>

      {stats.averageTimePerQuestion > 0 && (
        <View style={styles.additionalInfo}>
          <Text style={styles.additionalText}>
            平均每題用時：{Math.round(stats.averageTimePerQuestion)}秒
          </Text>
        </View>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT,
    marginBottom: 16,
    textAlign: 'center',
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 14,
    color: COLORS.SECONDARY_TEXT,
    marginTop: 4,
    textAlign: 'center',
  },
  additionalInfo: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    alignItems: 'center',
  },
  additionalText: {
    fontSize: 14,
    color: COLORS.SECONDARY_TEXT,
  },
});