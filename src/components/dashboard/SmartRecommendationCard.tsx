import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card, Button } from '../common';
import { COLORS } from '../../utils/constants';
import { OverallStats } from '../../types/statistics';

interface SmartRecommendationCardProps {
  stats: OverallStats;
  onStartWeakPointPractice: () => void;
}

export function SmartRecommendationCard({ stats, onStartWeakPointPractice }: SmartRecommendationCardProps) {
  // 計算弱點題目數量（這裡使用模擬數據，實際應該從錯題分析中獲取）
  const weakPointCount = 10;
  
  // 找出正確率最低的冊別作為推薦重點
  const weakestVolume = stats.volumeStats.reduce((weakest, current) => 
    current.accuracy < weakest.accuracy ? current : weakest
  );

  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Text style={styles.icon}>🎯</Text>
        </View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>每日弱點強攻</Text>
          <Text style={styles.subtitle}>基於個人化錯題分析</Text>
        </View>
      </View>

      <View style={styles.content}>
        <Text style={styles.challengeTitle}>
          今日挑戰：你的 {weakPointCount} 道高頻錯題
        </Text>
        
        <View style={styles.recommendationBox}>
          <Text style={styles.recommendationText}>
            重點加強：第{weakestVolume.volume}冊 (正確率 {weakestVolume.accuracy.toFixed(1)}%)
          </Text>
          <Text style={styles.benefitText}>
            完成後預計提升合格率 3-5%
          </Text>
        </View>

        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{weakPointCount}</Text>
            <Text style={styles.statLabel}>錯題待攻克</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>5-8</Text>
            <Text style={styles.statLabel}>預計用時(分鐘)</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>+3%</Text>
            <Text style={styles.statLabel}>預期提升</Text>
          </View>
        </View>

        <Button
          title="開始今日挑戰"
          onPress={onStartWeakPointPractice}
          style={styles.actionButton}
          textStyle={styles.actionButtonText}
        />
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.WARNING,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.WARNING + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  icon: {
    fontSize: 24,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT,
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
    color: COLORS.SECONDARY_TEXT,
  },
  content: {
    paddingTop: 8,
  },
  challengeTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.TEXT,
    marginBottom: 12,
    textAlign: 'center',
  },
  recommendationBox: {
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  recommendationText: {
    fontSize: 15,
    fontWeight: '500',
    color: COLORS.TEXT,
    marginBottom: 6,
  },
  benefitText: {
    fontSize: 14,
    color: COLORS.SUCCESS,
    fontWeight: '500',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
    paddingVertical: 12,
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: 12,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.WARNING,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.SECONDARY_TEXT,
    textAlign: 'center',
  },
  actionButton: {
    backgroundColor: COLORS.WARNING,
    borderRadius: 12,
    paddingVertical: 14,
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});
