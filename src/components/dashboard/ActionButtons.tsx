import React from 'react';
import { StyleSheet, View } from 'react-native';

import { COLORS } from '../../utils/constants';
import { Button } from '../common';

type ActionButtonsProps = {
  onStartExam: () => void;
  onStartWrongQuestionsReview: () => void;
};

export function ActionButtons({
  onStartExam,
  onStartWrongQuestionsReview,
}: ActionButtonsProps) {
  return (
    <View style={styles.container}>
      <Button
        title="開始考試"
        onPress={onStartExam}
        variant="primary"
        size="medium"
        style={styles.examButton}
      />
      <Button
        title="錯題複習"
        onPress={onStartWrongQuestionsReview}
        variant="secondary"
        size="medium"
        style={styles.reviewButton}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginBottom: 10,
  },
  examButton: {
    flex: 1,
    backgroundColor: COLORS.THEME,
    elevation: 2,
    shadowColor: COLORS.SHADOW,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  reviewButton: {
    flex: 1,
    backgroundColor: COLORS.CARD_BACKGROUND,
    borderColor: COLORS.BORDER,
    elevation: 1,
    shadowColor: COLORS.SHADOW,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
});