import React from 'react';
import {
  Modal as RNModal,
  View,
  Text,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import { Button } from './Button';

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  primaryAction?: {
    title: string;
    onPress: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
  };
  secondaryAction?: {
    title: string;
    onPress: () => void;
  };
  dismissible?: boolean;
}

export function Modal({
  visible,
  onClose,
  title,
  children,
  primaryAction,
  secondaryAction,
  dismissible = true,
}: ModalProps) {
  const handleBackdropPress = () => {
    if (dismissible) {
      onClose();
    }
  };

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={styles.backdrop}>
          <TouchableWithoutFeedback>
            <View style={styles.modal}>
              {title && (
                <View style={styles.header}>
                  <Text style={styles.title}>{title}</Text>
                </View>
              )}
              
              <View style={styles.content}>
                {children}
              </View>

              {(primaryAction || secondaryAction) && (
                <View style={styles.actions}>
                  {secondaryAction && (
                    <Button
                      title={secondaryAction.title}
                      onPress={secondaryAction.onPress}
                      variant="outline"
                      style={styles.actionButton}
                    />
                  )}
                  {primaryAction && (
                    <Button
                      title={primaryAction.title}
                      onPress={primaryAction.onPress}
                      variant={primaryAction.variant || 'primary'}
                      style={styles.actionButton}
                    />
                  )}
                </View>
              )}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </RNModal>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modal: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    maxWidth: 400,
    width: '100%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  header: {
    paddingTop: 24,
    paddingHorizontal: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1c1c1e',
    textAlign: 'center',
  },
  content: {
    padding: 24,
    flex: 1,
  },
  actions: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingBottom: 24,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});