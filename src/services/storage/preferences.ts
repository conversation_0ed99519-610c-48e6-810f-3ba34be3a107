import AsyncStorage from '@react-native-async-storage/async-storage';

// Simple MMKV-like interface using AsyncStorage
class Storage {
  async setString(key: string, value: string) {
    await AsyncStorage.setItem(key, value);
  }

  async getString(key: string): Promise<string | undefined> {
    const value = await AsyncStorage.getItem(key);
    return value ?? undefined;
  }

  async setBoolean(key: string, value: boolean) {
    await AsyncStorage.setItem(key, value.toString());
  }

  async getBoolean(key: string): Promise<boolean | undefined> {
    const value = await AsyncStorage.getItem(key);
    return value ? JSON.parse(value) : undefined;
  }

  async setNumber(key: string, value: number) {
    await AsyncStorage.setItem(key, value.toString());
  }

  async getNumber(key: string): Promise<number | undefined> {
    const value = await AsyncStorage.getItem(key);
    return value ? parseInt(value) : undefined;
  }

  async delete(key: string) {
    await AsyncStorage.removeItem(key);
  }

  async clearAll() {
    await AsyncStorage.clear();
  }
}

const storage = new Storage();

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  fontSize: 'small' | 'medium' | 'large';
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  autoNextQuestion: boolean;
  showExplanations: boolean;
  practiceMode: 'sequential' | 'random';
}

const DEFAULT_PREFERENCES: UserPreferences = {
  theme: 'auto',
  fontSize: 'medium',
  soundEnabled: true,
  vibrationEnabled: true,
  autoNextQuestion: false,
  showExplanations: true,
  practiceMode: 'sequential',
};

export const Preferences = {
  // Theme
  setTheme: async (theme: UserPreferences['theme']) => {
    await storage.setString('theme', theme);
  },
  getTheme: async (): Promise<UserPreferences['theme']> => {
    const value = await storage.getString('theme');
    return (value as UserPreferences['theme']) ?? DEFAULT_PREFERENCES.theme;
  },

  // Font Size
  setFontSize: async (fontSize: UserPreferences['fontSize']) => {
    await storage.setString('fontSize', fontSize);
  },
  getFontSize: async (): Promise<UserPreferences['fontSize']> => {
    const value = await storage.getString('fontSize');
    return (value as UserPreferences['fontSize']) ?? DEFAULT_PREFERENCES.fontSize;
  },

  // Sound
  setSoundEnabled: async (enabled: boolean) => {
    await storage.setBoolean('soundEnabled', enabled);
  },
  getSoundEnabled: async (): Promise<boolean> => {
    const value = await storage.getBoolean('soundEnabled');
    return value ?? DEFAULT_PREFERENCES.soundEnabled;
  },

  // Vibration
  setVibrationEnabled: async (enabled: boolean) => {
    await storage.setBoolean('vibrationEnabled', enabled);
  },
  getVibrationEnabled: async (): Promise<boolean> => {
    const value = await storage.getBoolean('vibrationEnabled');
    return value ?? DEFAULT_PREFERENCES.vibrationEnabled;
  },

  // Auto Next Question
  setAutoNextQuestion: async (enabled: boolean) => {
    await storage.setBoolean('autoNextQuestion', enabled);
  },
  getAutoNextQuestion: async (): Promise<boolean> => {
    const value = await storage.getBoolean('autoNextQuestion');
    return value ?? DEFAULT_PREFERENCES.autoNextQuestion;
  },

  // Show Explanations
  setShowExplanations: async (enabled: boolean) => {
    await storage.setBoolean('showExplanations', enabled);
  },
  getShowExplanations: async (): Promise<boolean> => {
    const value = await storage.getBoolean('showExplanations');
    return value ?? DEFAULT_PREFERENCES.showExplanations;
  },

  // Practice Mode
  setPracticeMode: async (mode: UserPreferences['practiceMode']) => {
    await storage.setString('practiceMode', mode);
  },
  getPracticeMode: async (): Promise<UserPreferences['practiceMode']> => {
    const value = await storage.getString('practiceMode');
    return (value as UserPreferences['practiceMode']) ?? DEFAULT_PREFERENCES.practiceMode;
  },

  // Last Practice Volume
  setLastPracticeVolume: async (volume: number) => {
    await storage.setNumber('lastPracticeVolume', volume);
  },
  getLastPracticeVolume: async (): Promise<number> => {
    const value = await storage.getNumber('lastPracticeVolume');
    return value ?? 1;
  },

  // Current Session ID (for recovery)
  setCurrentSessionId: async (sessionId: string | null) => {
    if (sessionId) {
      await storage.setString('currentSessionId', sessionId);
    } else {
      await storage.delete('currentSessionId');
    }
  },
  getCurrentSessionId: async (): Promise<string | null> => {
    const value = await storage.getString('currentSessionId');
    return value ?? null;
  },

  // Practice Progress (for quick resume)
  setPracticeProgress: async (sessionId: string, questionIndex: number) => {
    await storage.setNumber(`progress_${sessionId}`, questionIndex);
  },
  getPracticeProgress: async (sessionId: string): Promise<number> => {
    const value = await storage.getNumber(`progress_${sessionId}`);
    return value ?? 0;
  },
  clearPracticeProgress: async (sessionId: string) => {
    await storage.delete(`progress_${sessionId}`);
  },

  // Get All Preferences
  getAllPreferences: async (): Promise<UserPreferences> => {
    return {
      theme: await Preferences.getTheme(),
      fontSize: await Preferences.getFontSize(),
      soundEnabled: await Preferences.getSoundEnabled(),
      vibrationEnabled: await Preferences.getVibrationEnabled(),
      autoNextQuestion: await Preferences.getAutoNextQuestion(),
      showExplanations: await Preferences.getShowExplanations(),
      practiceMode: await Preferences.getPracticeMode(),
    };
  },

  // Reset All Preferences
  resetAllPreferences: async () => {
    const keys = Object.keys(DEFAULT_PREFERENCES).concat(['lastPracticeVolume', 'currentSessionId']);
    for (const key of keys) {
      await storage.delete(key);
    }
  },

  // Clear All Data (for debugging)
  clearAllData: async () => {
    await storage.clearAll();
  },
};