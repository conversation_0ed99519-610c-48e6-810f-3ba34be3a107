/**
 * Error Handling Service
 * Provides comprehensive error handling, validation, and reporting
 * Ensures graceful degradation and user-friendly error messages
 */

export enum ErrorCode {
  // Database Errors
  DATABASE_CONNECTION_FAILED = 'DB_CONNECTION_FAILED',
  DATABASE_QUERY_FAILED = 'DB_QUERY_FAILED',
  DATABASE_TRANSACTION_FAILED = 'DB_TRANSACTION_FAILED',
  
  // Session Errors
  SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  SESSION_CREATION_FAILED = 'SESSION_CREATION_FAILED',
  
  // Question Errors
  QUESTION_NOT_FOUND = 'QUESTION_NOT_FOUND',
  QUESTION_LOAD_FAILED = 'QUESTION_LOAD_FAILED',
  INVALID_QUESTION_DATA = 'INVALID_QUESTION_DATA',
  
  // Answer Errors
  ANSWER_SUBMISSION_FAILED = 'ANSWER_SUBMISSION_FAILED',
  INVALID_ANSWER_DATA = 'INVALID_ANSWER_DATA',
  
  // Volume Errors
  INVALID_VOLUME = 'INVALID_VOLUME',
  VOLUME_NOT_AVAILABLE = 'VOLUME_NOT_AVAILABLE',
  
  // Network Errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  
  // Validation Errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  
  // General Errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface AppError {
  code: ErrorCode;
  message: string;
  details?: any;
  timestamp: Date;
  context?: Record<string, any>;
  userMessage: string;
  recoverable: boolean;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export class ErrorHandler {
  private static instance: ErrorHandler;

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Create a standardized app error
   */
  createError(
    code: ErrorCode,
    message: string,
    details?: any,
    context?: Record<string, any>
  ): AppError {
    return {
      code,
      message,
      details,
      timestamp: new Date(),
      context,
      userMessage: this.getUserMessage(code, message),
      recoverable: this.isRecoverable(code)
    };
  }

  /**
   * Handle and log errors with appropriate action
   */
  handleError(error: Error | AppError, context?: Record<string, any>): AppError {
    let appError: AppError;

    if (this.isAppError(error)) {
      appError = error;
    } else {
      // Convert standard Error to AppError
      const errorCode = this.classifyError(error);
      appError = this.createError(
        errorCode,
        error.message,
        {
          stack: error.stack,
          name: error.name
        },
        context
      );
    }

    // Log the error
    this.logError(appError);

    // Additional handling based on error type
    this.processError(appError);

    return appError;
  }

  /**
   * Validate session data
   */
  validateSession(sessionData: any): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!sessionData) {
      errors.push({
        field: 'session',
        message: 'Session data is required'
      });
      return errors;
    }

    if (!sessionData.id || typeof sessionData.id !== 'string') {
      errors.push({
        field: 'id',
        message: 'Session ID must be a non-empty string',
        value: sessionData.id
      });
    }

    if (!sessionData.type || !['practice', 'exam'].includes(sessionData.type)) {
      errors.push({
        field: 'type',
        message: 'Session type must be either "practice" or "exam"',
        value: sessionData.type
      });
    }

    if (!Array.isArray(sessionData.volumes) || sessionData.volumes.length === 0) {
      errors.push({
        field: 'volumes',
        message: 'Volumes must be a non-empty array',
        value: sessionData.volumes
      });
    } else {
      // Validate individual volumes
      sessionData.volumes.forEach((volume: any, index: number) => {
        if (!Number.isInteger(volume) || volume < 1 || volume > 5) {
          errors.push({
            field: `volumes[${index}]`,
            message: 'Volume must be an integer between 1 and 5',
            value: volume
          });
        }
      });
    }

    if (sessionData.totalQuestions !== undefined && 
        (!Number.isInteger(sessionData.totalQuestions) || sessionData.totalQuestions <= 0)) {
      errors.push({
        field: 'totalQuestions',
        message: 'Total questions must be a positive integer',
        value: sessionData.totalQuestions
      });
    }

    return errors;
  }

  /**
   * Validate answer data
   */
  validateAnswer(answerData: any): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!answerData) {
      errors.push({
        field: 'answer',
        message: 'Answer data is required'
      });
      return errors;
    }

    if (!Number.isInteger(answerData.questionId) || answerData.questionId <= 0) {
      errors.push({
        field: 'questionId',
        message: 'Question ID must be a positive integer',
        value: answerData.questionId
      });
    }

    if (!Number.isInteger(answerData.volume) || answerData.volume < 1 || answerData.volume > 5) {
      errors.push({
        field: 'volume',
        message: 'Volume must be an integer between 1 and 5',
        value: answerData.volume
      });
    }

    if (!Number.isInteger(answerData.chapter) || answerData.chapter <= 0) {
      errors.push({
        field: 'chapter',
        message: 'Chapter must be a positive integer',
        value: answerData.chapter
      });
    }

    if (typeof answerData.isCorrect !== 'boolean') {
      errors.push({
        field: 'isCorrect',
        message: 'isCorrect must be a boolean value',
        value: answerData.isCorrect
      });
    }

    if (!['practice', 'exam'].includes(answerData.mode)) {
      errors.push({
        field: 'mode',
        message: 'Mode must be either "practice" or "exam"',
        value: answerData.mode
      });
    }

    if (!answerData.sessionId || typeof answerData.sessionId !== 'string') {
      errors.push({
        field: 'sessionId',
        message: 'Session ID must be a non-empty string',
        value: answerData.sessionId
      });
    }

    const validOptions = ['A', 'B', 'C', 'D'];
    if (!validOptions.includes(answerData.selectedOption)) {
      errors.push({
        field: 'selectedOption',
        message: 'Selected option must be A, B, C, or D',
        value: answerData.selectedOption
      });
    }

    if (!validOptions.includes(answerData.correctOption)) {
      errors.push({
        field: 'correctOption',
        message: 'Correct option must be A, B, C, or D',
        value: answerData.correctOption
      });
    }

    if (!Number.isInteger(answerData.timeSpent) || answerData.timeSpent < 0) {
      errors.push({
        field: 'timeSpent',
        message: 'Time spent must be a non-negative integer',
        value: answerData.timeSpent
      });
    }

    return errors;
  }

  /**
   * Validate volume availability
   */
  validateVolumeAvailability(volumes: number[]): ValidationError[] {
    const errors: ValidationError[] = [];
    const availableVolumes = [3, 4, 5]; // Currently available volumes

    volumes.forEach((volume, index) => {
      if (!availableVolumes.includes(volume)) {
        errors.push({
          field: `volumes[${index}]`,
          message: `Volume ${volume} is not currently available. Available volumes: ${availableVolumes.join(', ')}`,
          value: volume
        });
      }
    });

    return errors;
  }

  /**
   * Safe async operation wrapper with error handling
   */
  async safeAsync<T>(
    operation: () => Promise<T>,
    context?: Record<string, any>,
    fallback?: T
  ): Promise<{ success: true; data: T } | { success: false; error: AppError; fallback?: T }> {
    try {
      const data = await operation();
      return { success: true, data };
    } catch (error) {
      const appError = this.handleError(error as Error, context);
      return { 
        success: false, 
        error: appError, 
        fallback 
      };
    }
  }

  /**
   * Safe sync operation wrapper
   */
  safe<T>(
    operation: () => T,
    context?: Record<string, any>,
    fallback?: T
  ): { success: true; data: T } | { success: false; error: AppError; fallback?: T } {
    try {
      const data = operation();
      return { success: true, data };
    } catch (error) {
      const appError = this.handleError(error as Error, context);
      return { 
        success: false, 
        error: appError, 
        fallback 
      };
    }
  }

  private isAppError(error: any): error is AppError {
    return error && typeof error === 'object' && 'code' in error && 'userMessage' in error;
  }

  private classifyError(error: Error): ErrorCode {
    const message = error.message.toLowerCase();
    
    if (message.includes('database') || message.includes('sql') || message.includes('sqlite')) {
      if (message.includes('transaction')) {
        return ErrorCode.DATABASE_TRANSACTION_FAILED;
      } else if (message.includes('connection')) {
        return ErrorCode.DATABASE_CONNECTION_FAILED;
      }
      return ErrorCode.DATABASE_QUERY_FAILED;
    }
    
    if (message.includes('session')) {
      return ErrorCode.SESSION_CREATION_FAILED;
    }
    
    if (message.includes('question')) {
      return ErrorCode.QUESTION_LOAD_FAILED;
    }
    
    if (message.includes('network') || message.includes('fetch')) {
      return ErrorCode.NETWORK_ERROR;
    }

    return ErrorCode.UNKNOWN_ERROR;
  }

  private getUserMessage(code: ErrorCode, originalMessage: string): string {
    const userMessages: Record<ErrorCode, string> = {
      [ErrorCode.DATABASE_CONNECTION_FAILED]: '數據庫連接失敗，請重試',
      [ErrorCode.DATABASE_QUERY_FAILED]: '數據讀取失敗，請重試',
      [ErrorCode.DATABASE_TRANSACTION_FAILED]: '操作失敗，請重試',
      [ErrorCode.SESSION_NOT_FOUND]: '找不到練習紀錄，請重新開始',
      [ErrorCode.SESSION_EXPIRED]: '練習已過期，請重新開始',
      [ErrorCode.SESSION_CREATION_FAILED]: '無法開始練習，請重試',
      [ErrorCode.QUESTION_NOT_FOUND]: '找不到題目，請重試',
      [ErrorCode.QUESTION_LOAD_FAILED]: '題目載入失敗，請重試',
      [ErrorCode.INVALID_QUESTION_DATA]: '題目資料有誤，請重試',
      [ErrorCode.ANSWER_SUBMISSION_FAILED]: '答案提交失敗，請重試',
      [ErrorCode.INVALID_ANSWER_DATA]: '答案資料有誤，請重試',
      [ErrorCode.INVALID_VOLUME]: '無效的題冊選擇',
      [ErrorCode.VOLUME_NOT_AVAILABLE]: '此題冊暫未開放',
      [ErrorCode.NETWORK_ERROR]: '網路連接問題，請檢查網路連接',
      [ErrorCode.VALIDATION_ERROR]: '資料驗證失敗，請檢查輸入',
      [ErrorCode.UNKNOWN_ERROR]: '發生未知錯誤，請重試'
    };

    return userMessages[code] || '發生錯誤，請重試';
  }

  private isRecoverable(code: ErrorCode): boolean {
    const nonRecoverableErrors = [
      ErrorCode.INVALID_QUESTION_DATA,
      ErrorCode.INVALID_ANSWER_DATA,
      ErrorCode.VALIDATION_ERROR
    ];

    return !nonRecoverableErrors.includes(code);
  }

  private logError(error: AppError): void {
    const logData = {
      code: error.code,
      message: error.message,
      timestamp: error.timestamp.toISOString(),
      context: error.context,
      details: error.details
    };

    if (__DEV__) {
      console.error('App Error:', logData);
    } else {
      // In production, you might want to send to crash reporting service
      console.error('App Error:', error.code, error.message);
    }
  }

  private processError(error: AppError): void {
    // Additional processing based on error type
    switch (error.code) {
      case ErrorCode.DATABASE_CONNECTION_FAILED:
        // Could trigger database reconnection attempt
        break;
      case ErrorCode.SESSION_EXPIRED:
        // Could trigger automatic session cleanup
        break;
      case ErrorCode.NETWORK_ERROR:
        // Could trigger offline mode
        break;
    }
  }
}

export const errorHandler = ErrorHandler.getInstance();

// Convenience functions
export const validateSession = (data: any) => errorHandler.validateSession(data);
export const validateAnswer = (data: any) => errorHandler.validateAnswer(data);
export const validateVolumeAvailability = (volumes: number[]) => errorHandler.validateVolumeAvailability(volumes);
export const handleError = (error: Error, context?: Record<string, any>) => errorHandler.handleError(error, context);
export const safeAsync = <T>(op: () => Promise<T>, context?: Record<string, any>, fallback?: T) => 
  errorHandler.safeAsync(op, context, fallback);
export const safe = <T>(op: () => T, context?: Record<string, any>, fallback?: T) => 
  errorHandler.safe(op, context, fallback);