import shuffle from 'lodash.shuffle';
import { ProcessedQuestion, Question } from '../../types/question';
import { shouldNotShuffle } from '../../utils/questionUtils';

const questionFiles = {
  1: require('../../../assets/data/questions/volume1.json'),
  2: require('../../../assets/data/questions/volume2.json'),
  3: require('../../../assets/data/questions/volume3.json'),
  4: require('../../../assets/data/questions/volume4.json'),
  5: require('../../../assets/data/questions/volume5.json'),
};

export class QuestionLoader {
  private static loadedQuestions: Map<number, ProcessedQuestion[]> = new Map();

  static async loadVolume(volume: number): Promise<ProcessedQuestion[]> {
    if (volume < 1 || volume > 5) { // Now support 1-5
      throw new Error(`Invalid volume: ${volume}. Must be between 1 and 5.`);
    }

    // Check cache first
    const cached = this.loadedQuestions.get(volume);
    if (cached) {
      return cached;
    }

    try {
      const rawQuestions: Question[] = questionFiles[volume as keyof typeof questionFiles];

      // Process and shuffle questions
      const processedQuestions: ProcessedQuestion[] = rawQuestions.map((q, index) => {
        // Check if this question should not be shuffled due to cross-references
        const shouldPreserveCrossReferences = shouldNotShuffle(q.options);

        // Only shuffle options if they don't contain cross-references
        const finalOptions = shouldPreserveCrossReferences ? q.options : shuffle(q.options);

        return {
          id: q.id,
          volume,
          question: q.question,
          options: finalOptions,
          image: q.image_url,
          tags: q.tags,
        };
      });

      // Cache the questions
      this.loadedQuestions.set(volume, processedQuestions);

      return processedQuestions;
    } catch (error) {
      console.error(`Failed to load volume ${volume}:`, error);
      throw new Error(`Failed to load questions for volume ${volume}`);
    }
  }

  static async loadVolumeWithoutShuffle(volume: number): Promise<ProcessedQuestion[]> {
    if (volume < 1 || volume > 5) {
      throw new Error(`Invalid volume: ${volume}. Must be between 1 and 5.`);
    }

    try {
      const rawQuestions: Question[] = questionFiles[volume as keyof typeof questionFiles];

      // Process questions without shuffling options - we'll handle shuffling with mapping later
      const processedQuestions: ProcessedQuestion[] = rawQuestions.map((q) => {
        return {
          id: q.id,
          volume,
          question: q.question,
          options: q.options, // Keep original order
          image: q.image_url,
          tags: q.tags,
        };
      });

      return processedQuestions;
    } catch (error) {
      console.error(`Failed to load volume ${volume}:`, error);
      throw new Error(`Failed to load questions for volume ${volume}`);
    }
  }

  static async loadMultipleVolumes(volumes: number[]): Promise<ProcessedQuestion[]> {
    try {
      const allQuestions: ProcessedQuestion[] = [];
      
      for (const volume of volumes) {
        const volumeQuestions = await this.loadVolume(volume);
        allQuestions.push(...volumeQuestions);
      }
      
      return allQuestions;
    } catch (error) {
      console.error('Failed to load multiple volumes:', error);
      throw error;
    }
  }

  static async loadAllVolumes(): Promise<ProcessedQuestion[]> {
    return this.loadMultipleVolumes([1, 2, 3, 4, 5]); // All 5 volumes
  }

  static getVolumeInfo(volume: number) {
    const cached = this.loadedQuestions.get(volume);
    return {
      volume,
      loaded: !!cached,
      questionCount: cached?.length || 0,
    };
  }

  static clearCache(volume?: number) {
    if (volume) {
      this.loadedQuestions.delete(volume);
    } else {
      this.loadedQuestions.clear();
    }
  }
}