// AUTO-GENERATED FILE. Do not edit manually.
// Run: node scripts/generate-image-map.js
// Maps logical image paths from JSON (e.g. "volume1/1.png") to static require(...) so RN can bundle images.

export const IMAGE_MAP = {
  "volume1/1.png": require("../../../assets/data/questions/volume1/1.png"),
  "volume1/2.png": require("../../../assets/data/questions/volume1/2.png"),
  "volume1/3.png": require("../../../assets/data/questions/volume1/3.png"),
  "volume1/4.png": require("../../../assets/data/questions/volume1/4.png"),
  "volume1/5.png": require("../../../assets/data/questions/volume1/5.png"),
  "volume1/6.png": require("../../../assets/data/questions/volume1/6.png"),
  "volume1/7.png": require("../../../assets/data/questions/volume1/7.png"),
  "volume1/8.png": require("../../../assets/data/questions/volume1/8.png"),
  "volume1/9.png": require("../../../assets/data/questions/volume1/9.png"),
  "volume1/10.png": require("../../../assets/data/questions/volume1/10.png"),
  "volume1/11.png": require("../../../assets/data/questions/volume1/11.png"),
  "volume1/12.png": require("../../../assets/data/questions/volume1/12.png"),
  "volume1/13.png": require("../../../assets/data/questions/volume1/13.png"),
  "volume1/14.png": require("../../../assets/data/questions/volume1/14.png"),
  "volume1/15.png": require("../../../assets/data/questions/volume1/15.png"),
  "volume1/16.png": require("../../../assets/data/questions/volume1/16.png"),
  "volume1/17.png": require("../../../assets/data/questions/volume1/17.png"),
  "volume1/18.png": require("../../../assets/data/questions/volume1/18.png"),
  "volume1/19.png": require("../../../assets/data/questions/volume1/19.png"),
  "volume1/20.png": require("../../../assets/data/questions/volume1/20.png"),
  "volume1/21.png": require("../../../assets/data/questions/volume1/21.png"),
  "volume1/22.png": require("../../../assets/data/questions/volume1/22.png"),
  "volume1/23.png": require("../../../assets/data/questions/volume1/23.png"),
  "volume1/24.png": require("../../../assets/data/questions/volume1/24.png"),
  "volume1/25.png": require("../../../assets/data/questions/volume1/25.png"),
  "volume1/26.png": require("../../../assets/data/questions/volume1/26.png"),
  "volume1/27.png": require("../../../assets/data/questions/volume1/27.png"),
  "volume1/28.png": require("../../../assets/data/questions/volume1/28.png"),
  "volume1/29.png": require("../../../assets/data/questions/volume1/29.png"),
  "volume1/30.png": require("../../../assets/data/questions/volume1/30.png"),
  "volume1/31.png": require("../../../assets/data/questions/volume1/31.png"),
  "volume1/32.png": require("../../../assets/data/questions/volume1/32.png"),
  "volume1/33.png": require("../../../assets/data/questions/volume1/33.png"),
  "volume1/34.png": require("../../../assets/data/questions/volume1/34.png"),
  "volume1/35.png": require("../../../assets/data/questions/volume1/35.png"),
  "volume1/36.png": require("../../../assets/data/questions/volume1/36.png"),
  "volume1/37.png": require("../../../assets/data/questions/volume1/37.png"),
  "volume1/38.png": require("../../../assets/data/questions/volume1/38.png"),
  "volume1/39.png": require("../../../assets/data/questions/volume1/39.png"),
  "volume1/40.png": require("../../../assets/data/questions/volume1/40.png"),
  "volume1/41.png": require("../../../assets/data/questions/volume1/41.png"),
  "volume1/42.png": require("../../../assets/data/questions/volume1/42.png"),
  "volume1/43.png": require("../../../assets/data/questions/volume1/43.png"),
  "volume1/44.png": require("../../../assets/data/questions/volume1/44.png"),
  "volume1/45.png": require("../../../assets/data/questions/volume1/45.png"),
  "volume1/46.png": require("../../../assets/data/questions/volume1/46.png"),
  "volume1/47.png": require("../../../assets/data/questions/volume1/47.png"),
  "volume1/48.png": require("../../../assets/data/questions/volume1/48.png"),
  "volume1/49.png": require("../../../assets/data/questions/volume1/49.png"),
  "volume1/50.png": require("../../../assets/data/questions/volume1/50.png"),
  "volume1/51.png": require("../../../assets/data/questions/volume1/51.png"),
  "volume1/52.png": require("../../../assets/data/questions/volume1/52.png"),
  "volume1/53.png": require("../../../assets/data/questions/volume1/53.png"),
  "volume1/54.png": require("../../../assets/data/questions/volume1/54.png"),
  "volume1/55.png": require("../../../assets/data/questions/volume1/55.png"),
  "volume1/56.png": require("../../../assets/data/questions/volume1/56.png"),
  "volume1/57.png": require("../../../assets/data/questions/volume1/57.png"),
  "volume1/58.png": require("../../../assets/data/questions/volume1/58.png"),
  "volume1/59.png": require("../../../assets/data/questions/volume1/59.png"),
  "volume1/60.png": require("../../../assets/data/questions/volume1/60.png"),
  "volume1/61.png": require("../../../assets/data/questions/volume1/61.png"),
  "volume1/62.png": require("../../../assets/data/questions/volume1/62.png"),
  "volume1/63.png": require("../../../assets/data/questions/volume1/63.png"),
  "volume1/64.png": require("../../../assets/data/questions/volume1/64.png"),
  "volume1/65.png": require("../../../assets/data/questions/volume1/65.png"),
  "volume1/66.png": require("../../../assets/data/questions/volume1/66.png"),
  "volume1/67.png": require("../../../assets/data/questions/volume1/67.png"),
  "volume1/68.png": require("../../../assets/data/questions/volume1/68.png"),
  "volume1/69.png": require("../../../assets/data/questions/volume1/69.png"),
  "volume1/70.png": require("../../../assets/data/questions/volume1/70.png"),
  "volume1/71.png": require("../../../assets/data/questions/volume1/71.png"),
  "volume1/72.png": require("../../../assets/data/questions/volume1/72.png"),
  "volume1/73.png": require("../../../assets/data/questions/volume1/73.png"),
  "volume1/74.png": require("../../../assets/data/questions/volume1/74.png"),
  "volume1/75.png": require("../../../assets/data/questions/volume1/75.png"),
  "volume1/76.png": require("../../../assets/data/questions/volume1/76.png"),
  "volume1/77.png": require("../../../assets/data/questions/volume1/77.png"),
  "volume1/78.png": require("../../../assets/data/questions/volume1/78.png"),
  "volume1/79.png": require("../../../assets/data/questions/volume1/79.png"),
  "volume1/80.png": require("../../../assets/data/questions/volume1/80.png"),
  "volume1/81.png": require("../../../assets/data/questions/volume1/81.png"),
  "volume1/82.png": require("../../../assets/data/questions/volume1/82.png"),
  "volume1/83.png": require("../../../assets/data/questions/volume1/83.png"),
  "volume1/84.png": require("../../../assets/data/questions/volume1/84.png"),
  "volume1/85.png": require("../../../assets/data/questions/volume1/85.png"),
  "volume1/86.png": require("../../../assets/data/questions/volume1/86.png"),
  "volume1/87.png": require("../../../assets/data/questions/volume1/87.png"),
  "volume1/88.png": require("../../../assets/data/questions/volume1/88.png"),
  "volume1/89.png": require("../../../assets/data/questions/volume1/89.png"),
  "volume1/90.png": require("../../../assets/data/questions/volume1/90.png"),
  "volume1/91.png": require("../../../assets/data/questions/volume1/91.png"),
  "volume1/92.png": require("../../../assets/data/questions/volume1/92.png"),
  "volume1/93.png": require("../../../assets/data/questions/volume1/93.png"),
  "volume1/94.png": require("../../../assets/data/questions/volume1/94.png"),
  "volume1/95.png": require("../../../assets/data/questions/volume1/95.png"),
  "volume1/96.png": require("../../../assets/data/questions/volume1/96.png"),
  "volume1/97.png": require("../../../assets/data/questions/volume1/97.png"),
  "volume1/98.png": require("../../../assets/data/questions/volume1/98.png"),
  "volume1/99.png": require("../../../assets/data/questions/volume1/99.png"),
  "volume1/100.png": require("../../../assets/data/questions/volume1/100.png"),
  "volume1/101.png": require("../../../assets/data/questions/volume1/101.png"),
  "volume1/102.png": require("../../../assets/data/questions/volume1/102.png"),
  "volume1/103.png": require("../../../assets/data/questions/volume1/103.png"),
  "volume1/104.png": require("../../../assets/data/questions/volume1/104.png"),
  "volume1/105.png": require("../../../assets/data/questions/volume1/105.png"),
  "volume1/106.png": require("../../../assets/data/questions/volume1/106.png"),
  "volume1/107.png": require("../../../assets/data/questions/volume1/107.png"),
  "volume1/108.png": require("../../../assets/data/questions/volume1/108.png"),
  "volume1/109.png": require("../../../assets/data/questions/volume1/109.png"),
  "volume1/110.png": require("../../../assets/data/questions/volume1/110.png"),
  "volume1/111.png": require("../../../assets/data/questions/volume1/111.png"),
  "volume1/112.png": require("../../../assets/data/questions/volume1/112.png"),
  "volume1/113.png": require("../../../assets/data/questions/volume1/113.png"),
  "volume1/114.png": require("../../../assets/data/questions/volume1/114.png"),
  "volume1/115.png": require("../../../assets/data/questions/volume1/115.png"),
  "volume1/116.png": require("../../../assets/data/questions/volume1/116.png"),
  "volume1/117.png": require("../../../assets/data/questions/volume1/117.png"),
  "volume1/118.png": require("../../../assets/data/questions/volume1/118.png"),
  "volume1/119.png": require("../../../assets/data/questions/volume1/119.png"),
  "volume1/120.png": require("../../../assets/data/questions/volume1/120.png"),
  "volume1/121.png": require("../../../assets/data/questions/volume1/121.png"),
  "volume1/122.png": require("../../../assets/data/questions/volume1/122.png"),
  "volume1/123.png": require("../../../assets/data/questions/volume1/123.png"),
  "volume1/124.png": require("../../../assets/data/questions/volume1/124.png"),
  "volume1/125.png": require("../../../assets/data/questions/volume1/125.png"),
  "volume1/126.png": require("../../../assets/data/questions/volume1/126.png"),
  "volume1/127.png": require("../../../assets/data/questions/volume1/127.png"),
  "volume1/128.png": require("../../../assets/data/questions/volume1/128.png"),
  "volume1/129.png": require("../../../assets/data/questions/volume1/129.png"),
  "volume1/130.png": require("../../../assets/data/questions/volume1/130.png"),
  "volume1/131.png": require("../../../assets/data/questions/volume1/131.png"),
  "volume1/132.png": require("../../../assets/data/questions/volume1/132.png"),
  "volume1/133.png": require("../../../assets/data/questions/volume1/133.png"),
  "volume1/134.png": require("../../../assets/data/questions/volume1/134.png"),
  "volume1/135.png": require("../../../assets/data/questions/volume1/135.png"),
  "volume1/136.png": require("../../../assets/data/questions/volume1/136.png"),
  "volume1/137.png": require("../../../assets/data/questions/volume1/137.png"),
  "volume1/138.png": require("../../../assets/data/questions/volume1/138.png"),
  "volume1/139.png": require("../../../assets/data/questions/volume1/139.png"),
  "volume1/140.png": require("../../../assets/data/questions/volume1/140.png"),
  "volume1/141.png": require("../../../assets/data/questions/volume1/141.png"),
  "volume1/142.png": require("../../../assets/data/questions/volume1/142.png"),
  "volume1/143.png": require("../../../assets/data/questions/volume1/143.png"),
  "volume1/144.png": require("../../../assets/data/questions/volume1/144.png"),
  "volume1/145.png": require("../../../assets/data/questions/volume1/145.png"),
  "volume1/146.png": require("../../../assets/data/questions/volume1/146.png"),
  "volume1/147.png": require("../../../assets/data/questions/volume1/147.png"),
  "volume1/148.png": require("../../../assets/data/questions/volume1/148.png"),
  "volume1/149.png": require("../../../assets/data/questions/volume1/149.png"),
  "volume1/150.png": require("../../../assets/data/questions/volume1/150.png"),
  "volume1/151.png": require("../../../assets/data/questions/volume1/151.png"),
  "volume1/152.png": require("../../../assets/data/questions/volume1/152.png"),
  "volume1/153.png": require("../../../assets/data/questions/volume1/153.png"),
  "volume1/154.png": require("../../../assets/data/questions/volume1/154.png"),
  "volume1/155.png": require("../../../assets/data/questions/volume1/155.png"),
  "volume1/156.png": require("../../../assets/data/questions/volume1/156.png"),
  "volume1/157.png": require("../../../assets/data/questions/volume1/157.png"),
  "volume1/158.png": require("../../../assets/data/questions/volume1/158.png"),
  "volume1/159.png": require("../../../assets/data/questions/volume1/159.png"),
  "volume1/160.png": require("../../../assets/data/questions/volume1/160.png"),
  "volume1/161.png": require("../../../assets/data/questions/volume1/161.png"),
  "volume1/162.png": require("../../../assets/data/questions/volume1/162.png"),
  "volume1/163.png": require("../../../assets/data/questions/volume1/163.png"),
  "volume1/164.png": require("../../../assets/data/questions/volume1/164.png"),
  "volume1/165.png": require("../../../assets/data/questions/volume1/165.png"),
  "volume1/166.png": require("../../../assets/data/questions/volume1/166.png"),
  "volume1/167.png": require("../../../assets/data/questions/volume1/167.png"),
  "volume1/168.png": require("../../../assets/data/questions/volume1/168.png"),
  "volume1/169.png": require("../../../assets/data/questions/volume1/169.png"),
  "volume1/170.png": require("../../../assets/data/questions/volume1/170.png"),
  "volume1/171.png": require("../../../assets/data/questions/volume1/171.png"),
  "volume1/172.png": require("../../../assets/data/questions/volume1/172.png"),
  "volume1/173.png": require("../../../assets/data/questions/volume1/173.png"),
  "volume1/174.png": require("../../../assets/data/questions/volume1/174.png"),
  "volume1/175.png": require("../../../assets/data/questions/volume1/175.png"),
  "volume1/176.png": require("../../../assets/data/questions/volume1/176.png"),
  "volume2/1.png": require("../../../assets/data/questions/volume2/1.png"),
  "volume2/2.png": require("../../../assets/data/questions/volume2/2.png"),
  "volume2/3.png": require("../../../assets/data/questions/volume2/3.png"),
  "volume2/4.png": require("../../../assets/data/questions/volume2/4.png"),
  "volume2/5.png": require("../../../assets/data/questions/volume2/5.png"),
  "volume2/6.png": require("../../../assets/data/questions/volume2/6.png"),
  "volume2/7.png": require("../../../assets/data/questions/volume2/7.png"),
  "volume2/8.png": require("../../../assets/data/questions/volume2/8.png"),
  "volume2/9.png": require("../../../assets/data/questions/volume2/9.png"),
  "volume2/10.png": require("../../../assets/data/questions/volume2/10.png"),
  "volume2/11.png": require("../../../assets/data/questions/volume2/11.png"),
  "volume2/12.png": require("../../../assets/data/questions/volume2/12.png"),
  "volume2/13.png": require("../../../assets/data/questions/volume2/13.png"),
  "volume2/14.png": require("../../../assets/data/questions/volume2/14.png"),
  "volume2/15.png": require("../../../assets/data/questions/volume2/15.png"),
  "volume2/16.png": require("../../../assets/data/questions/volume2/16.png"),
  "volume2/17.png": require("../../../assets/data/questions/volume2/17.png"),
  "volume2/18.png": require("../../../assets/data/questions/volume2/18.png"),
  "volume2/19.png": require("../../../assets/data/questions/volume2/19.png"),
  "volume2/20.png": require("../../../assets/data/questions/volume2/20.png"),
  "volume2/21.png": require("../../../assets/data/questions/volume2/21.png"),
  "volume2/22.png": require("../../../assets/data/questions/volume2/22.png"),
  "volume2/23.png": require("../../../assets/data/questions/volume2/23.png"),
  "volume2/24.png": require("../../../assets/data/questions/volume2/24.png"),
  "volume2/25.png": require("../../../assets/data/questions/volume2/25.png"),
  "volume2/26.png": require("../../../assets/data/questions/volume2/26.png"),
  "volume2/27.png": require("../../../assets/data/questions/volume2/27.png"),
  "volume2/28.png": require("../../../assets/data/questions/volume2/28.png"),
  "volume2/29.png": require("../../../assets/data/questions/volume2/29.png"),
  "volume2/30.png": require("../../../assets/data/questions/volume2/30.png"),
  "volume2/31.png": require("../../../assets/data/questions/volume2/31.png"),
  "volume2/32.png": require("../../../assets/data/questions/volume2/32.png"),
  "volume2/33.png": require("../../../assets/data/questions/volume2/33.png"),
  "volume2/34.png": require("../../../assets/data/questions/volume2/34.png"),
  "volume2/35.png": require("../../../assets/data/questions/volume2/35.png"),
  "volume2/36.png": require("../../../assets/data/questions/volume2/36.png"),
  "volume2/37.png": require("../../../assets/data/questions/volume2/37.png"),
  "volume2/38.png": require("../../../assets/data/questions/volume2/38.png"),
  "volume2/39.png": require("../../../assets/data/questions/volume2/39.png"),
  "volume2/40.png": require("../../../assets/data/questions/volume2/40.png"),
  "volume2/41.png": require("../../../assets/data/questions/volume2/41.png"),
  "volume2/42.png": require("../../../assets/data/questions/volume2/42.png"),
} as const;

export type ImageKey = keyof typeof IMAGE_MAP;
