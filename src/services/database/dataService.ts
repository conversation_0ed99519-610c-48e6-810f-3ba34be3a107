/**
 * Unified Database Service
 * Provides consistent data access layer for all database operations
 * Maintains data consistency between answer_records and question_stats tables
 */

import { getDatabase } from './init';
import { errorHandler, validateAnswer, ErrorCode } from '../error/errorHandler';

export interface AnswerRecord {
  id?: number;
  questionId: number;
  volume: number;
  chapter: number;
  isCorrect: boolean;
  mode: 'practice' | 'exam';
  sessionId: string;
  selectedOption: string;
  correctOption: string;
  timeSpent: number;
  createdAt?: string;
}

export interface QuestionStats {
  questionId: number;
  volume: number;
  totalAttempts: number;
  correctAttempts: number;
  wrongAttempts: number;
  lastAttempted?: string;
  isBookmarked: boolean;
  note?: string;
}

export class DataService {
  private static instance: DataService;

  static getInstance(): DataService {
    if (!DataService.instance) {
      DataService.instance = new DataService();
    }
    return DataService.instance;
  }

  private getDB() {
    return getDatabase();
  }

  /**
   * Record an answer and update all related statistics atomically
   * This ensures data consistency across tables
   */
  async recordAnswer(answer: AnswerRecord): Promise<void> {
    // Validate answer data first
    const validationErrors = validateAnswer(answer);
    if (validationErrors.length > 0) {
      throw errorHandler.createError(
        ErrorCode.INVALID_ANSWER_DATA,
        'Answer validation failed',
        validationErrors,
        { answer }
      );
    }

    const db = this.getDB();
    
    try {
      // Use withTransactionAsync for proper transaction management
      await db.withTransactionAsync(async () => {
        // 1. Insert answer record
        await db.runAsync(
          `INSERT INTO answer_records (
            question_id, volume, chapter, is_correct, mode, session_id,
            selected_option, correct_option, time_spent
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            answer.questionId,
            answer.volume,
            answer.chapter,
            answer.isCorrect ? 1 : 0,
            answer.mode,
            answer.sessionId,
            answer.selectedOption,
            answer.correctOption,
            answer.timeSpent
          ]
        );

        // 2. Update question stats directly within this transaction
        await this.updateQuestionStatsInTransaction(db, {
          questionId: answer.questionId,
          volume: answer.volume,
          isCorrect: answer.isCorrect
        });
      });
    } catch (error) {
      const appError = errorHandler.handleError(error as Error, { 
        operation: 'recordAnswer',
        answer 
      });
      
      if (appError.code === ErrorCode.UNKNOWN_ERROR) {
        appError.code = ErrorCode.ANSWER_SUBMISSION_FAILED;
      }
      
      throw appError;
    }
  }

  /**
   * Update question statistics within an existing transaction
   * This method doesn't start its own transaction - it must be called within one
   */
  private async updateQuestionStatsInTransaction(db: any, data: {
    questionId: number;
    volume: number;
    isCorrect: boolean;
  }): Promise<void> {
    try {
      // Try composite primary key approach first (question_id, volume)
      await db.runAsync(
        `INSERT INTO question_stats 
         (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted)
         VALUES (?, ?, 1, ?, ?, datetime('now'))
         ON CONFLICT(question_id, volume) DO UPDATE SET
           total_attempts = total_attempts + 1,
           correct_attempts = correct_attempts + ?,
           wrong_attempts = wrong_attempts + ?,
           last_attempted = datetime('now')`,
        [
          data.questionId,
          data.volume,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
        ]
      );
    } catch {
      // Fallback to single primary key approach (question_id only)
      await db.runAsync(
        `INSERT INTO question_stats 
         (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted)
         VALUES (?, ?, 1, ?, ?, datetime('now'))
         ON CONFLICT(question_id) DO UPDATE SET
           volume = ?,
           total_attempts = total_attempts + 1,
           correct_attempts = correct_attempts + ?,
           wrong_attempts = wrong_attempts + ?,
           last_attempted = datetime('now')`,
        [
          data.questionId,
          data.volume,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
          data.volume,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
        ]
      );
    }
  }

  /**
   * Get seen question IDs for given volumes
   * Uses answer_records as single source of truth
   */
  async getSeenQuestionIds(volumes: number[]): Promise<Set<number>> {
    try {
      const placeholders = volumes.map(() => '?').join(',');
      const result = await this.getDB().getAllAsync(
        `SELECT DISTINCT question_id FROM answer_records
         WHERE volume IN (${placeholders})`,
        volumes
      ) as { question_id: number }[];

      return new Set(result.map(row => row.question_id));
    } catch (error) {
      console.error('Failed to get seen question IDs:', error);
      throw error;
    }
  }

  /**
   * Get wrong question IDs (questions answered incorrectly and never correctly)
   */
  async getWrongQuestionIds(volumes: number[]): Promise<Set<number>> {
    try {
      const placeholders = volumes.map(() => '?').join(',');
      const result = await this.getDB().getAllAsync(
        `SELECT question_id
         FROM answer_records 
         WHERE volume IN (${placeholders})
         GROUP BY question_id 
         HAVING SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) = 0 
         AND SUM(CASE WHEN is_correct = 0 THEN 1 ELSE 0 END) > 0`,
        volumes
      ) as { question_id: number }[];
      
      return new Set(result.map(row => row.question_id));
    } catch (error) {
      console.error('Failed to get wrong question IDs:', error);
      throw error;
    }
  }

  /**
   * Get bookmarked question IDs
   */
  async getBookmarkedQuestionIds(volumes: number[]): Promise<Set<number>> {
    try {
      const placeholders = volumes.map(() => '?').join(',');
      const result = await this.getDB().getAllAsync(
        `SELECT DISTINCT question_id FROM question_stats
         WHERE volume IN (${placeholders}) AND is_bookmarked = 1`,
        volumes
      ) as { question_id: number }[];
      
      return new Set(result.map(row => row.question_id));
    } catch (error) {
      console.error('Failed to get bookmarked question IDs:', error);
      throw error;
    }
  }

  /**
   * Toggle bookmark status for a question
   */
  async toggleBookmark(questionId: number, volume: number): Promise<boolean> {
    try {
      // First, ensure the question stats record exists
      await this.getDB().runAsync(
        `INSERT INTO question_stats (question_id, volume, total_attempts, correct_attempts, wrong_attempts, is_bookmarked)
         VALUES (?, ?, 0, 0, 0, 0)
         ON CONFLICT(question_id, volume) DO NOTHING`,
        [questionId, volume]
      );

      // Toggle bookmark status
      await this.getDB().runAsync(
        `UPDATE question_stats 
         SET is_bookmarked = CASE WHEN is_bookmarked = 1 THEN 0 ELSE 1 END
         WHERE question_id = ? AND volume = ?`,
        [questionId, volume]
      );

      // Get the new bookmark status
      const bookmarkStatus = await this.getDB().getFirstAsync(
        `SELECT is_bookmarked FROM question_stats WHERE question_id = ? AND volume = ?`,
        [questionId, volume]
      ) as { is_bookmarked: number } | null;

      return bookmarkStatus?.is_bookmarked === 1;
    } catch (error) {
      console.error('Failed to toggle bookmark:', error);
      throw error;
    }
  }

  /**
   * Get question statistics
   */
  async getQuestionStats(questionId: number, volume: number): Promise<QuestionStats | null> {
    try {
      const result = await this.getDB().getFirstAsync(
        `SELECT * FROM question_stats WHERE question_id = ? AND volume = ?`,
        [questionId, volume]
      ) as any;

      if (!result) return null;

      return {
        questionId: result.question_id,
        volume: result.volume,
        totalAttempts: result.total_attempts,
        correctAttempts: result.correct_attempts,
        wrongAttempts: result.wrong_attempts,
        lastAttempted: result.last_attempted,
        isBookmarked: result.is_bookmarked === 1,
        note: result.note
      };
    } catch (error) {
      console.error('Failed to get question stats:', error);
      throw error;
    }
  }

  /**
   * Update question note
   */
  async updateQuestionNote(questionId: number, volume: number, note: string): Promise<void> {
    try {
      // Ensure record exists first
      await this.getDB().runAsync(
        `INSERT INTO question_stats (question_id, volume, total_attempts, correct_attempts, wrong_attempts, note)
         VALUES (?, ?, 0, 0, 0, ?)
         ON CONFLICT(question_id, volume) DO UPDATE SET note = ?`,
        [questionId, volume, note, note]
      );
    } catch (error) {
      console.error('Failed to update question note:', error);
      throw error;
    }
  }
}

export const dataService = DataService.getInstance();