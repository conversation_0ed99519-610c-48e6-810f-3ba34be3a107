/**
 * Session Management Service
 * Handles persistence and retrieval of practice and exam sessions
 * Ensures proper session lifecycle management
 */

import { getDatabase } from './init';
import { errorHandler, validateSession, ErrorCode } from '../error/errorHandler';

export interface SessionData {
  id: string;
  type: 'practice' | 'exam';
  title: string;
  totalQuestions: number;
  correctCount: number;
  wrongCount: number;
  durationSeconds: number;
  volumes: number[];
  config: any; // PracticeConfig | ExamConfig
  isCompleted: boolean;
  createdAt: string;
  completedAt?: string;
}

export class SessionManager {
  private static instance: SessionManager;

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  private getDB() {
    return getDatabase();
  }

  /**
   * Create a new session record
   */
  async createSession(sessionData: {
    id: string;
    type: 'practice' | 'exam';
    title: string;
    totalQuestions: number;
    volumes: number[];
    config: any;
  }): Promise<void> {
    // Validate session data
    const validationErrors = validateSession(sessionData);
    if (validationErrors.length > 0) {
      throw errorHandler.createError(
        ErrorCode.VALIDATION_ERROR,
        'Session validation failed',
        validationErrors,
        { sessionData }
      );
    }

    try {
      await this.getDB().runAsync(
        `INSERT INTO sessions (
          id, type, title, total_questions, volumes, config, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, datetime('now'))`,
        [
          sessionData.id,
          sessionData.type,
          sessionData.title,
          sessionData.totalQuestions,
          JSON.stringify(sessionData.volumes),
          JSON.stringify(sessionData.config)
        ]
      );
    } catch (error) {
      const appError = errorHandler.handleError(error as Error, {
        operation: 'createSession',
        sessionData
      });
      
      if (appError.code === ErrorCode.UNKNOWN_ERROR) {
        appError.code = ErrorCode.SESSION_CREATION_FAILED;
      }
      
      throw appError;
    }
  }

  /**
   * Update session progress
   */
  async updateSessionProgress(sessionId: string, correctCount: number, wrongCount: number): Promise<void> {
    try {
      await this.getDB().runAsync(
        `UPDATE sessions 
         SET correct_count = ?, wrong_count = ?
         WHERE id = ?`,
        [correctCount, wrongCount, sessionId]
      );
    } catch (error) {
      console.error('Failed to update session progress:', error);
      throw error;
    }
  }

  /**
   * Complete a session
   */
  async completeSession(sessionId: string, finalStats: {
    correctCount: number;
    wrongCount: number;
    durationSeconds: number;
  }): Promise<void> {
    try {
      await this.getDB().runAsync(
        `UPDATE sessions 
         SET correct_count = ?, wrong_count = ?, duration_seconds = ?, 
             is_completed = 1, completed_at = datetime('now')
         WHERE id = ?`,
        [
          finalStats.correctCount,
          finalStats.wrongCount,
          finalStats.durationSeconds,
          sessionId
        ]
      );
    } catch (error) {
      console.error('Failed to complete session:', error);
      throw error;
    }
  }

  /**
   * Get session data
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      const result = await this.getDB().getFirstAsync(
        `SELECT * FROM sessions WHERE id = ?`,
        [sessionId]
      ) as any;

      if (!result) return null;

      return {
        id: result.id,
        type: result.type,
        title: result.title,
        totalQuestions: result.total_questions,
        correctCount: result.correct_count || 0,
        wrongCount: result.wrong_count || 0,
        durationSeconds: result.duration_seconds || 0,
        volumes: JSON.parse(result.volumes || '[]'),
        config: JSON.parse(result.config || '{}'),
        isCompleted: result.is_completed === 1,
        createdAt: result.created_at,
        completedAt: result.completed_at,
      };
    } catch (error) {
      console.error('Failed to get session:', error);
      throw error;
    }
  }

  /**
   * Get recent sessions
   */
  async getRecentSessions(type?: 'practice' | 'exam', limit = 10): Promise<SessionData[]> {
    try {
      const query = type 
        ? `SELECT * FROM sessions WHERE type = ? ORDER BY created_at DESC LIMIT ?`
        : `SELECT * FROM sessions ORDER BY created_at DESC LIMIT ?`;
      
      const params = type ? [type, limit] : [limit];
      
      const results = await this.getDB().getAllAsync(query, params) as any[];

      return results.map(result => ({
        id: result.id,
        type: result.type,
        title: result.title,
        totalQuestions: result.total_questions,
        correctCount: result.correct_count || 0,
        wrongCount: result.wrong_count || 0,
        durationSeconds: result.duration_seconds || 0,
        volumes: JSON.parse(result.volumes || '[]'),
        config: JSON.parse(result.config || '{}'),
        isCompleted: result.is_completed === 1,
        createdAt: result.created_at,
        completedAt: result.completed_at,
      }));
    } catch (error) {
      console.error('Failed to get recent sessions:', error);
      throw error;
    }
  }

  /**
   * Delete old sessions (cleanup)
   */
  async cleanupOldSessions(daysOld = 30): Promise<void> {
    try {
      await this.getDB().runAsync(
        `DELETE FROM sessions 
         WHERE created_at < datetime('now', '-' || ? || ' days')`,
        [daysOld]
      );
    } catch (error) {
      console.error('Failed to cleanup old sessions:', error);
      throw error;
    }
  }

  /**
   * Get session statistics
   */
  async getSessionStats(): Promise<{
    totalSessions: number;
    practiceSessionsToday: number;
    examSessionsToday: number;
    averageAccuracy: number;
  }> {
    try {
      const totalResult = await this.getDB().getFirstAsync(
        `SELECT COUNT(*) as total FROM sessions`
      ) as { total: number };

      const todayResult = await this.getDB().getAllAsync(
        `SELECT type, COUNT(*) as count 
         FROM sessions 
         WHERE date(created_at) = date('now') 
         GROUP BY type`
      ) as { type: string; count: number }[];

      const accuracyResult = await this.getDB().getFirstAsync(
        `SELECT 
           AVG(CASE WHEN (correct_count + wrong_count) > 0 
               THEN (correct_count * 100.0 / (correct_count + wrong_count)) 
               ELSE 0 END) as avg_accuracy
         FROM sessions 
         WHERE is_completed = 1`
      ) as { avg_accuracy: number };

      const practiceToday = todayResult.find(r => r.type === 'practice')?.count || 0;
      const examToday = todayResult.find(r => r.type === 'exam')?.count || 0;

      return {
        totalSessions: totalResult.total,
        practiceSessionsToday: practiceToday,
        examSessionsToday: examToday,
        averageAccuracy: accuracyResult.avg_accuracy || 0
      };
    } catch (error) {
      console.error('Failed to get session stats:', error);
      throw error;
    }
  }
}

export const sessionManager = SessionManager.getInstance();