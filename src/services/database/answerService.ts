import { AnswerRecord } from '../../types/database';
import { getDatabase } from './init';

export interface AnswerData {
  questionId: number;
  volume: number;
  chapter: number;
  isCorrect: boolean;
  mode: 'practice' | 'exam';
  sessionId: string;
  selectedOption: 'A' | 'B' | 'C' | 'D';
  correctOption: 'A' | 'B' | 'C' | 'D';
  timeSpent: number;
}

export async function recordAnswer(answer: AnswerData): Promise<void> {
  const { dataService } = await import('./dataService');
  
  try {
    await dataService.recordAnswer({
      questionId: answer.questionId,
      volume: answer.volume,
      chapter: answer.chapter,
      isCorrect: answer.isCorrect,
      mode: answer.mode,
      sessionId: answer.sessionId,
      selectedOption: answer.selectedOption,
      correctOption: answer.correctOption,
      timeSpent: answer.timeSpent,
    });
    
    // Update volume progress separately for now (will be integrated later)
    await updateVolumeProgress(answer);
  } catch (error) {
    console.error('Failed to record answer:', error);
    throw error;
  }
}

// updateQuestionStats function removed - now handled by DataService.updateQuestionStatsInTransaction

async function updateVolumeProgress(answer: AnswerData): Promise<void> {
  const db = getDatabase();
  
  await db.runAsync(
    `UPDATE volume_progress 
     SET seen_count = (
       SELECT COUNT(DISTINCT question_id) 
       FROM answer_records 
       WHERE volume = ?
     ),
     correct_count = (
       SELECT COUNT(*) 
       FROM answer_records 
       WHERE volume = ? AND is_correct = 1
     ),
     wrong_count = (
       SELECT COUNT(*) 
       FROM answer_records 
       WHERE volume = ? AND is_correct = 0
     ),
     last_practice = datetime('now'),
     updated_at = datetime('now')
     WHERE volume = ?`,
    [answer.volume, answer.volume, answer.volume, answer.volume]
  );
}

export async function getAnswerHistory(sessionId: string): Promise<AnswerRecord[]> {
  const db = getDatabase();
  
  const result = await db.getAllAsync(
    `SELECT * FROM answer_records 
     WHERE session_id = ? 
     ORDER BY created_at ASC`,
    [sessionId]
  ) as AnswerRecord[];
  
  return result;
}