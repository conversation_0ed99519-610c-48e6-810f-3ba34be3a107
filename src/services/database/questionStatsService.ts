/**
 * Question Statistics Service
 * Handles individual question performance tracking and analytics
 * Provides insights into question difficulty and user performance patterns
 */

import { getDatabase } from './init';

export interface QuestionStat {
  questionId: number;
  volume: number;
  totalAttempts: number;
  correctAttempts: number;
  wrongAttempts: number;
  accuracy: number;
  lastAttempted?: string;
}

export interface QuestionPerformanceData {
  questionId: number;
  volume: number;
  chapter: number;
  userAttempts: number;
  userCorrect: number;
  userAccuracy: number;
  globalAttempts: number;
  globalAccuracy: number;
  difficulty: 'easy' | 'medium' | 'hard';
  lastAttempted?: string;
}

export class QuestionStatsService {
  private static instance: QuestionStatsService;

  static getInstance(): QuestionStatsService {
    if (!QuestionStatsService.instance) {
      QuestionStatsService.instance = new QuestionStatsService();
    }
    return QuestionStatsService.instance;
  }

  private getDB() {
    return getDatabase();
  }

  /**
   * Update question statistics after an answer (with own transaction)
   * Use this when calling from outside of an existing transaction
   */
  async updateQuestionStats(data: {
    questionId: number;
    volume: number;
    chapter: number;
    isCorrect: boolean;
  }): Promise<void> {
    try {
      const db = this.getDB();
      
      // Use atomic transaction to ensure consistency
      await db.withTransactionAsync(async () => {
        await this.updateQuestionStatsInTransaction(db, data);
      });
    } catch (error) {
      console.error('Failed to update question stats:', error);
      throw error;
    }
  }

  /**
   * Update question statistics within an existing transaction
   * Use this when already inside a transaction to avoid nesting
   */
  async updateQuestionStatsInTransaction(db: any, data: {
    questionId: number;
    volume: number;
    isCorrect: boolean;
  }): Promise<void> {
    // Try composite primary key approach first (question_id, volume)
    try {
      await db.runAsync(
        `INSERT INTO question_stats 
         (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted)
         VALUES (?, ?, 1, ?, ?, datetime('now'))
         ON CONFLICT(question_id, volume) DO UPDATE SET
           total_attempts = total_attempts + 1,
           correct_attempts = correct_attempts + ?,
           wrong_attempts = wrong_attempts + ?,
           last_attempted = datetime('now')`,
        [
          data.questionId,
          data.volume,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
        ]
      );
    } catch {
      // Fallback to single primary key approach (question_id only)
      await db.runAsync(
        `INSERT INTO question_stats 
         (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted)
         VALUES (?, ?, 1, ?, ?, datetime('now'))
         ON CONFLICT(question_id) DO UPDATE SET
           volume = ?,
           total_attempts = total_attempts + 1,
           correct_attempts = correct_attempts + ?,
           wrong_attempts = wrong_attempts + ?,
           last_attempted = datetime('now')`,
        [
          data.questionId,
          data.volume,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
          data.volume,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
        ]
      );
    }
  }

  /**
   * Get statistics for a specific question
   */
  async getQuestionStats(questionId: number, volume: number): Promise<QuestionStat | null> {
    try {
      const result = await this.getDB().getFirstAsync(
        `SELECT * FROM question_stats 
         WHERE question_id = ? AND volume = ?`,
        [questionId, volume]
      ) as any;

      if (!result) return null;

      return {
        questionId: result.question_id,
        volume: result.volume,
        totalAttempts: result.total_attempts,
        correctAttempts: result.correct_attempts,
        wrongAttempts: result.wrong_attempts,
        accuracy: result.total_attempts > 0 
          ? (result.correct_attempts / result.total_attempts) * 100 
          : 0,
        lastAttempted: result.last_attempted
      };
    } catch (error) {
      console.error('Failed to get question stats:', error);
      throw error;
    }
  }

  /**
   * Get question performance data with global context
   */
  async getQuestionPerformanceData(questionId: number, volume: number): Promise<QuestionPerformanceData | null> {
    try {
      const db = this.getDB();
      
      // Get user-specific data from answer_records
      const userResult = await db.getFirstAsync(
        `SELECT 
           COUNT(*) as user_attempts,
           SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as user_correct,
           MAX(created_at) as last_attempted,
           MAX(chapter) as chapter
         FROM answer_records 
         WHERE question_id = ? AND volume = ?`,
        [questionId, volume]
      ) as any;

      // Get global stats from question_stats
      const globalResult = await db.getFirstAsync(
        `SELECT total_attempts, correct_attempts 
         FROM question_stats 
         WHERE question_id = ? AND volume = ?`,
        [questionId, volume]
      ) as any;

      if (!userResult && !globalResult) return null;

      const userAttempts = userResult?.user_attempts || 0;
      const userCorrect = userResult?.user_correct || 0;
      const userAccuracy = userAttempts > 0 ? (userCorrect / userAttempts) * 100 : 0;
      
      const globalAttempts = globalResult?.total_attempts || 0;
      const globalCorrect = globalResult?.correct_attempts || 0;
      const globalAccuracy = globalAttempts > 0 ? (globalCorrect / globalAttempts) * 100 : 0;

      // Determine difficulty based on global accuracy
      let difficulty: 'easy' | 'medium' | 'hard' = 'medium';
      if (globalAccuracy >= 80) difficulty = 'easy';
      else if (globalAccuracy <= 40) difficulty = 'hard';

      return {
        questionId,
        volume,
        chapter: userResult?.chapter || 1,
        userAttempts,
        userCorrect,
        userAccuracy,
        globalAttempts,
        globalAccuracy,
        difficulty,
        lastAttempted: userResult?.last_attempted
      };
    } catch (error) {
      console.error('Failed to get question performance data:', error);
      throw error;
    }
  }

  /**
   * Get top difficult questions for a volume
   */
  async getDifficultQuestions(volume: number, limit = 10): Promise<QuestionStat[]> {
    try {
      const results = await this.getDB().getAllAsync(
        `SELECT * FROM question_stats 
         WHERE volume = ? AND total_attempts >= 3
         ORDER BY (correct_attempts * 1.0 / total_attempts) ASC, total_attempts DESC 
         LIMIT ?`,
        [volume, limit]
      ) as any[];

      return results.map(result => ({
        questionId: result.question_id,
        volume: result.volume,
        totalAttempts: result.total_attempts,
        correctAttempts: result.correct_attempts,
        wrongAttempts: result.wrong_attempts,
        accuracy: (result.correct_attempts / result.total_attempts) * 100,
        lastAttempted: result.last_attempted
      }));
    } catch (error) {
      console.error('Failed to get difficult questions:', error);
      throw error;
    }
  }

  /**
   * Get user's weakest questions based on answer history
   */
  async getUserWeakestQuestions(volume?: number, limit = 10): Promise<QuestionPerformanceData[]> {
    try {
      const whereClause = volume ? 'WHERE volume = ?' : '';
      const params = volume ? [volume, limit] : [limit];
      
      const query = `
        SELECT 
          question_id,
          volume,
          MAX(chapter) as chapter,
          COUNT(*) as user_attempts,
          SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as user_correct,
          MAX(created_at) as last_attempted
        FROM answer_records 
        ${whereClause}
        GROUP BY question_id, volume
        HAVING user_attempts >= 2
        ORDER BY (user_correct * 1.0 / user_attempts) ASC, user_attempts DESC
        LIMIT ?`;

      const results = await this.getDB().getAllAsync(query, params) as any[];

      const performanceData: QuestionPerformanceData[] = [];
      
      for (const result of results) {
        // Get global stats for context
        const globalStats = await this.getQuestionStats(result.question_id, result.volume);
        
        const userAccuracy = (result.user_correct / result.user_attempts) * 100;
        const globalAccuracy = globalStats?.accuracy || 0;
        
        let difficulty: 'easy' | 'medium' | 'hard' = 'medium';
        if (globalAccuracy >= 80) difficulty = 'easy';
        else if (globalAccuracy <= 40) difficulty = 'hard';

        performanceData.push({
          questionId: result.question_id,
          volume: result.volume,
          chapter: result.chapter,
          userAttempts: result.user_attempts,
          userCorrect: result.user_correct,
          userAccuracy,
          globalAttempts: globalStats?.totalAttempts || 0,
          globalAccuracy,
          difficulty,
          lastAttempted: result.last_attempted
        });
      }

      return performanceData;
    } catch (error) {
      console.error('Failed to get user weakest questions:', error);
      throw error;
    }
  }

  /**
   * Get volume statistics summary
   */
  async getVolumeStatsummary(volume: number): Promise<{
    totalQuestions: number;
    attemptedQuestions: number;
    averageAccuracy: number;
    totalAttempts: number;
    easyQuestions: number;
    mediumQuestions: number;
    hardQuestions: number;
  }> {
    try {
      const db = this.getDB();
      
      // Get basic stats
      const basicStats = await db.getFirstAsync(
        `SELECT 
           COUNT(*) as attempted_questions,
           AVG(correct_attempts * 1.0 / total_attempts) * 100 as avg_accuracy,
           SUM(total_attempts) as total_attempts
         FROM question_stats 
         WHERE volume = ?`,
        [volume]
      ) as any;

      // Get difficulty distribution
      const difficultyStats = await db.getAllAsync(
        `SELECT 
           CASE 
             WHEN (correct_attempts * 1.0 / total_attempts) >= 0.8 THEN 'easy'
             WHEN (correct_attempts * 1.0 / total_attempts) <= 0.4 THEN 'hard'
             ELSE 'medium'
           END as difficulty,
           COUNT(*) as count
         FROM question_stats 
         WHERE volume = ? AND total_attempts >= 3
         GROUP BY difficulty`,
        [volume]
      ) as any[];

      const difficultyMap = difficultyStats.reduce((acc: any, stat: any) => {
        acc[stat.difficulty] = stat.count;
        return acc;
      }, {});

      return {
        totalQuestions: 0, // This would need to come from question data
        attemptedQuestions: basicStats.attempted_questions || 0,
        averageAccuracy: basicStats.avg_accuracy || 0,
        totalAttempts: basicStats.total_attempts || 0,
        easyQuestions: difficultyMap.easy || 0,
        mediumQuestions: difficultyMap.medium || 0,
        hardQuestions: difficultyMap.hard || 0
      };
    } catch (error) {
      console.error('Failed to get volume stats summary:', error);
      throw error;
    }
  }

  /**
   * Recalculate all question stats from answer_records
   * Useful for data migration or integrity checks
   */
  async recalculateAllQuestionStats(): Promise<void> {
    try {
      const db = this.getDB();
      
      await db.withTransactionAsync(async () => {
        // Clear existing stats
        await db.runAsync('DELETE FROM question_stats');
        
        // Recalculate from answer_records
        await db.runAsync(`
          INSERT INTO question_stats (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted)
          SELECT 
            question_id,
            volume,
            COUNT(*) as total_attempts,
            SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_attempts,
            SUM(CASE WHEN is_correct = 0 THEN 1 ELSE 0 END) as wrong_attempts,
            MAX(created_at) as last_attempted
          FROM answer_records
          GROUP BY question_id, volume
        `);
      });
    } catch (error) {
      console.error('Failed to recalculate question stats:', error);
      throw error;
    }
  }
}

export const questionStatsService = QuestionStatsService.getInstance();