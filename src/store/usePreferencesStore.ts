import { create } from 'zustand';
import { Preferences, UserPreferences } from '../services/storage/preferences';

interface PreferencesState extends UserPreferences {
  isLoaded: boolean;
  // Actions
  updateTheme: (theme: UserPreferences['theme']) => Promise<void>;
  updateFontSize: (fontSize: UserPreferences['fontSize']) => Promise<void>;
  updateSoundEnabled: (enabled: boolean) => Promise<void>;
  updateVibrationEnabled: (enabled: boolean) => Promise<void>;
  updateAutoNextQuestion: (enabled: boolean) => Promise<void>;
  updateShowExplanations: (enabled: boolean) => Promise<void>;
  updatePracticeMode: (mode: UserPreferences['practiceMode']) => Promise<void>;
  loadPreferences: () => Promise<void>;
  resetPreferences: () => Promise<void>;
}

export const usePreferencesStore = create<PreferencesState>((set) => ({
  // Initial state - default values
  theme: 'auto',
  fontSize: 'medium',
  soundEnabled: true,
  vibrationEnabled: true,
  autoNextQuestion: false,
  showExplanations: true,
  practiceMode: 'sequential',
  isLoaded: false,

  updateTheme: async (theme) => {
    await Preferences.setTheme(theme);
    set({ theme });
  },

  updateFontSize: async (fontSize) => {
    await Preferences.setFontSize(fontSize);
    set({ fontSize });
  },

  updateSoundEnabled: async (enabled) => {
    await Preferences.setSoundEnabled(enabled);
    set({ soundEnabled: enabled });
  },

  updateVibrationEnabled: async (enabled) => {
    await Preferences.setVibrationEnabled(enabled);
    set({ vibrationEnabled: enabled });
  },

  updateAutoNextQuestion: async (enabled) => {
    await Preferences.setAutoNextQuestion(enabled);
    set({ autoNextQuestion: enabled });
  },

  updateShowExplanations: async (enabled) => {
    await Preferences.setShowExplanations(enabled);
    set({ showExplanations: enabled });
  },

  updatePracticeMode: async (mode) => {
    await Preferences.setPracticeMode(mode);
    set({ practiceMode: mode });
  },

  loadPreferences: async () => {
    try {
      const prefs = await Preferences.getAllPreferences();
      set({ ...prefs, isLoaded: true });
    } catch (error) {
      console.error('Failed to load preferences:', error);
      set({ isLoaded: true }); // Set loaded even on error to prevent infinite loading
    }
  },

  resetPreferences: async () => {
    try {
      await Preferences.resetAllPreferences();
      const prefs = await Preferences.getAllPreferences();
      set(prefs);
    } catch (error) {
      console.error('Failed to reset preferences:', error);
    }
  },
}));