import { create } from 'zustand';
import { ProcessedQuestion } from '../types/question';
import { ExamConfig, ExamPaperMap, ExamResult, ExamSession, ExamSetResult, ExamStats } from '../types/session';
import { EXAM_RULES } from '../utils/constants';

interface ExamState {
  // Session data
  session: ExamSession | null;
  isActive: boolean;
  isPaused: boolean;

  // Current question state
  currentQuestion: ProcessedQuestion | null;
  selectedAnswer: number | null;
  answerStartTime: Date | null;

  // Exam stats and timing
  stats: ExamStats;
  examResult: ExamResult | null;
  timerId: NodeJS.Timeout | null; // Add timer ID to track active timer
  isAutoSubmitted: boolean; // Flag to track auto-submission
  
  // Actions
  startExam: (examData: { questions: ProcessedQuestion[]; paperMap: ExamPaperMap }) => void;
  selectAnswer: (answerIndex: number) => void;
  nextQuestion: () => void;
  previousQuestion: () => void;
  jumpToQuestion: (index: number) => void;
  pauseExam: () => void;
  resumeExam: () => void;
  submitExam: () => ExamResult;
  calculateResult: () => ExamResult;
  reset: () => void;
}

const createInitialStats = (): ExamStats => ({
  totalQuestions: 0,
  answeredCount: 0,
  correctCount: 0,
  wrongCount: 0,
  accuracy: 0,
  timeSpent: 0,
  timeRemaining: EXAM_RULES.TIME_LIMIT_MINUTES * 60 - 1, // in seconds
  currentSet: 1,
  setProgress: {
    set1: { answered: 0, total: EXAM_RULES.QUESTIONS_PER_VOLUME[1] },
    set2: { answered: 0, total: EXAM_RULES.QUESTIONS_PER_VOLUME[2] },
    set3: { answered: 0, total: EXAM_RULES.QUESTIONS_PER_VOLUME[3] },
    set4: { answered: 0, total: EXAM_RULES.QUESTIONS_PER_VOLUME[4] },
    set5: { answered: 0, total: EXAM_RULES.QUESTIONS_PER_VOLUME[5] },
  },
});

const createExamConfig = (): ExamConfig => ({
  totalQuestions: EXAM_RULES.TOTAL_QUESTIONS,
  timeLimitMinutes: EXAM_RULES.TIME_LIMIT_MINUTES,
  questionsPerSet: {
    set1: EXAM_RULES.QUESTIONS_PER_VOLUME[1],
    set2: EXAM_RULES.QUESTIONS_PER_VOLUME[2],
    set3: EXAM_RULES.QUESTIONS_PER_VOLUME[3],
    set4: EXAM_RULES.QUESTIONS_PER_VOLUME[4],
    set5: EXAM_RULES.QUESTIONS_PER_VOLUME[5],
  },
  maxWrongTotal: EXAM_RULES.MAX_WRONG_TOTAL,
  maxWrongPerSet: EXAM_RULES.MAX_WRONG_PER_VOLUME,
});

export const useExamStore = create<ExamState>((set, get) => ({
  session: null,
  isActive: false,
  isPaused: false,
  currentQuestion: null,
  selectedAnswer: null,
  answerStartTime: null,
  stats: createInitialStats(),
  examResult: null,
  timerId: null, // Initialize timerId
  isAutoSubmitted: false, // Initialize auto-submission flag

  startExam: (examData) => {
    const { questions, paperMap } = examData;
    const sessionId = `exam_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const config = createExamConfig();

    // Clear any existing timer first
    const currentState = get();
    if (currentState.timerId) {
      clearInterval(currentState.timerId);
    }

    const session: ExamSession = {
      id: sessionId,
      title: '澳門考車理論考試',
      questions,
      currentIndex: 0,
      answers: {},
      startTime: new Date(),
      timeLimit: config.timeLimitMinutes,
      config,
      paperMap,
    };

    set({
      session,
      isActive: true,
      isPaused: false,
      currentQuestion: questions[0] || null,
      selectedAnswer: null,
      answerStartTime: new Date(),
      stats: {
        ...createInitialStats(),
        totalQuestions: questions.length,
      },
      examResult: null,
      timerId: null, // Will be set below
      isAutoSubmitted: false, // Reset auto-submission flag
    });
    
    // Start timer countdown
    const timerId = setInterval(() => {
      const state = get();
      if (!state.isActive || state.isPaused) return;
      
      const newTimeRemaining = Math.max(0, state.stats.timeRemaining - 1);
      set(prev => ({
        stats: {
          ...prev.stats,
          timeRemaining: newTimeRemaining,
          timeSpent: prev.stats.timeSpent + 1,
        }
      }));
      
      // Auto-submit when time is up
      if (newTimeRemaining === 0) {
        clearInterval(timerId);
        // Set auto-submission flag before submitting
        set(prev => ({ ...prev, isAutoSubmitted: true }));
        get().submitExam();
      }
    }, 1000);
    
    // Store the timer ID for cleanup
    set(prev => ({ ...prev, timerId: timerId as unknown as NodeJS.Timeout }));
  },

  selectAnswer: (answerIndex) => {
    const { session, currentQuestion } = get();
    if (!session || !currentQuestion) return;

    // Convert displayed answer index to original option index using paper map
    const examIndex = session.currentIndex;
    const optionMap = session.paperMap.optionMaps[examIndex];
    const originalAnswerIndex = optionMap ? optionMap.displayToOriginal[answerIndex] : answerIndex;

    // Update answer in session (store original option index)
    const updatedSession = {
      ...session,
      answers: {
        ...session.answers,
        [examIndex]: originalAnswerIndex,
      }
    };

    // Get current set from paper map
    const questionMap = session.paperMap.questionMap[examIndex];
    const currentSet = questionMap ? questionMap.setNumber : 3; // fallback to set 3

    // Calculate current set progress
    const setProgress = { ...get().stats.setProgress };
    const setKey = `set${currentSet}` as keyof typeof setProgress;

    // Check if this is a new answer
    const wasAnswered = session.answers[examIndex] !== undefined;
    if (!wasAnswered) {
      setProgress[setKey].answered += 1;
    }

    set({
      session: updatedSession,
      selectedAnswer: answerIndex, // Keep displayed index for UI
      stats: {
        ...get().stats,
        currentSet,
        setProgress,
        answeredCount: Object.keys(updatedSession.answers).length,
      }
    });
  },

  nextQuestion: () => {
    const { session } = get();
    if (!session) return;

    const nextIndex = session.currentIndex + 1;
    if (nextIndex >= session.questions.length) return;

    const nextQuestion = session.questions[nextIndex];
    const existingOriginalAnswer = session.answers[nextIndex];

    // Get current set from paper map
    const questionMap = session.paperMap.questionMap[nextIndex];
    const currentSet = questionMap ? questionMap.setNumber : 3;

    // Convert original answer back to displayed answer for UI
    let selectedAnswer = null;
    if (existingOriginalAnswer !== undefined) {
      const optionMap = session.paperMap.optionMaps[nextIndex];
      selectedAnswer = optionMap ? optionMap.originalToDisplay[existingOriginalAnswer] : existingOriginalAnswer;
    }

    set({
      session: {
        ...session,
        currentIndex: nextIndex,
      },
      currentQuestion: nextQuestion,
      selectedAnswer,
      answerStartTime: new Date(),
      stats: {
        ...get().stats,
        currentSet,
      }
    });
  },

  previousQuestion: () => {
    const { session } = get();
    if (!session || session.currentIndex <= 0) return;

    const prevIndex = session.currentIndex - 1;
    const prevQuestion = session.questions[prevIndex];
    const existingOriginalAnswer = session.answers[prevIndex];

    // Get current set from paper map
    const questionMap = session.paperMap.questionMap[prevIndex];
    const currentSet = questionMap ? questionMap.setNumber : 3;

    // Convert original answer back to displayed answer for UI
    let selectedAnswer = null;
    if (existingOriginalAnswer !== undefined) {
      const optionMap = session.paperMap.optionMaps[prevIndex];
      selectedAnswer = optionMap ? optionMap.originalToDisplay[existingOriginalAnswer] : existingOriginalAnswer;
    }

    set({
      session: {
        ...session,
        currentIndex: prevIndex,
      },
      currentQuestion: prevQuestion,
      selectedAnswer,
      answerStartTime: new Date(),
      stats: {
        ...get().stats,
        currentSet,
      }
    });
  },

  jumpToQuestion: (index) => {
    const { session } = get();
    if (!session || index < 0 || index >= session.questions.length) return;

    const question = session.questions[index];
    const existingOriginalAnswer = session.answers[index];

    // Get current set from paper map
    const questionMap = session.paperMap.questionMap[index];
    const currentSet = questionMap ? questionMap.setNumber : 3;

    // Convert original answer back to displayed answer for UI
    let selectedAnswer = null;
    if (existingOriginalAnswer !== undefined) {
      const optionMap = session.paperMap.optionMaps[index];
      selectedAnswer = optionMap ? optionMap.originalToDisplay[existingOriginalAnswer] : existingOriginalAnswer;
    }

    set({
      session: {
        ...session,
        currentIndex: index,
      },
      currentQuestion: question,
      selectedAnswer,
      answerStartTime: new Date(),
      stats: {
        ...get().stats,
        currentSet,
      }
    });
  },

  pauseExam: () => {
    set({ isPaused: true });
  },

  resumeExam: () => {
    set({ 
      isPaused: false,
      answerStartTime: new Date(),
    });
  },

  calculateResult: () => {
    const { session } = get();
    if (!session) {
      throw new Error('No active exam session');
    }

    // Group questions by set using the paper map
    const questionsBySet: { [setNumber: number]: { question: ProcessedQuestion; examIndex: number }[] } = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: []
    };

    session.questions.forEach((question, examIndex) => {
      const questionMap = session.paperMap.questionMap[examIndex];
      if (questionMap) {
        questionsBySet[questionMap.setNumber].push({ question, examIndex });
      }
    });

    // Sort questions within each set by their exam index to maintain exam order
    Object.keys(questionsBySet).forEach(setKey => {
      const setNumber = parseInt(setKey);
      questionsBySet[setNumber].sort((a, b) => a.examIndex - b.examIndex);
    });

    const calculateSetResult = (setQuestions: { question: ProcessedQuestion; examIndex: number }[], setNumber: number): ExamSetResult => {
      let correctCount = 0;
      const userAnswers: number[] = [];
      const questions: ProcessedQuestion[] = [];

      setQuestions.forEach(({ question, examIndex }) => {
        // Keep the shuffled options order as shown during the exam
        // The question.options are already in the shuffled order from the exam session
        const displayQuestion = question;

        questions.push(displayQuestion);
        const originalUserAnswer = session.answers[examIndex];

        // Convert original answer index to displayed answer index for result display
        let displayUserAnswer = -1;
        if (originalUserAnswer !== undefined) {
          const optionMap = session.paperMap.optionMaps[examIndex];
          displayUserAnswer = optionMap ? optionMap.originalToDisplay[originalUserAnswer] : originalUserAnswer;
        }
        userAnswers.push(displayUserAnswer);

        // Check if answer is correct using the original answer index
        if (originalUserAnswer !== undefined && question.options.find(opt => opt.isCorrect)) {
          // Find the correct option in the shuffled options
          const correctShuffledIndex = question.options.findIndex(opt => opt.isCorrect);
          const optionMap = session.paperMap.optionMaps[examIndex];
          const correctOriginalIndex = optionMap ? optionMap.displayToOriginal[correctShuffledIndex] : correctShuffledIndex;

          if (originalUserAnswer === correctOriginalIndex) {
            correctCount++;
          }
        }
      });

      const setNames = ['第1冊', '第2冊', '第3冊', '第4冊', '第5冊'];
      const setVolumes = [[1], [2], [3], [4], [5]];

      return {
        name: setNames[setNumber - 1],
        volumes: setVolumes[setNumber - 1],
        totalQuestions: questions.length,
        correctCount,
        wrongCount: questions.length - correctCount,
        questions,
        userAnswers,
      };
    };

    const set1Result = calculateSetResult(questionsBySet[1], 1);
    const set2Result = calculateSetResult(questionsBySet[2], 2);
    const set3Result = calculateSetResult(questionsBySet[3], 3);
    const set4Result = calculateSetResult(questionsBySet[4], 4);
    const set5Result = calculateSetResult(questionsBySet[5], 5);

    const totalCorrect = set1Result.correctCount + set2Result.correctCount + set3Result.correctCount + set4Result.correctCount + set5Result.correctCount;
    const totalWrong = set1Result.wrongCount + set2Result.wrongCount + set3Result.wrongCount + set4Result.wrongCount + set5Result.wrongCount;
    
    // Check failure conditions
    let failureReason: ExamResult['failureReason'];
    let isPassed = true;
    
    if (totalWrong > EXAM_RULES.MAX_WRONG_TOTAL) {
      isPassed = false;
      failureReason = 'too_many_wrong_total';
    } else if (
      set1Result.wrongCount > EXAM_RULES.MAX_WRONG_PER_VOLUME ||
      set2Result.wrongCount > EXAM_RULES.MAX_WRONG_PER_VOLUME ||
      set3Result.wrongCount > EXAM_RULES.MAX_WRONG_PER_VOLUME ||
      set4Result.wrongCount > EXAM_RULES.MAX_WRONG_PER_VOLUME ||
      set5Result.wrongCount > EXAM_RULES.MAX_WRONG_PER_VOLUME
    ) {
      isPassed = false;
      failureReason = 'too_many_wrong_per_set';
    }
    
    const result: ExamResult = {
      sessionId: session.id,
      isPassed,
      score: totalCorrect,
      totalQuestions: session.questions.length,
      correctCount: totalCorrect,
      wrongCount: totalWrong,
      timeSpent: get().stats.timeSpent,
      failureReason,
      setResults: {
        set1: set1Result,
        set2: set2Result,
        set3: set3Result,
        set4: set4Result,
        set5: set5Result,
      },
    };
    
    return result;
  },

  submitExam: () => {
    const { session, timerId } = get();
    if (!session) {
      throw new Error('No active exam session');
    }
    
    // Clear the timer when exam is submitted
    if (timerId) {
      clearInterval(timerId);
    }
    
    const result = get().calculateResult();
    
    set({
      session: {
        ...session,
        endTime: new Date(),
      },
      isActive: false,
      examResult: result,
      timerId: null, // Clear timer ID
    });
    
    return result;
  },

  reset: () => {
    // Clear any existing timer
    const { timerId } = get();
    if (timerId) {
      clearInterval(timerId);
    }
    
    set({
      session: null,
      isActive: false,
      isPaused: false,
      currentQuestion: null,
      selectedAnswer: null,
      answerStartTime: null,
      stats: createInitialStats(),
      examResult: null,
      timerId: null, // Clear timer ID
      isAutoSubmitted: false, // Reset auto-submission flag
    });
  },
})); 