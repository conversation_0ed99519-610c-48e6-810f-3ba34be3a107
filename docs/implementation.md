# 澳門考車神器 App - 優化版項目結構

## 📁 優化版文件夾結構

```
macao-driving-test-app/
├── 📱 app/                          # Expo Router 頁面
│   ├── index.tsx                   # 主頁（儀表板）
│   ├── practice/                   # 練習模式
│   │   ├── index.tsx               # 練習選項頁
│   │   └── session.tsx             # 練習進行頁面
│   ├── exam/                       # 考試模式
│   │   ├── index.tsx               # 考試說明頁
│   │   ├── session.tsx             # 考試進行頁面
│   │   ├── result.tsx              # 考試結果頁（單次）
│   │   └── history/                # 歷史記錄
│   │       ├── index.tsx           # 考試歷史列表
│   │       └── [sessionId].tsx    # 動態路由：查看特定考試詳情
│   ├── review/                     # 複習功能
│   │   ├── wrong-questions.tsx    # 錯題集
│   │   └── bookmarks.tsx          # 收藏題目
│   ├── profile/                    # 個人設定
│   │   └── index.tsx               # 設定頁面
│   ├── _layout.tsx                 # 根佈局
│   └── +not-found.tsx              # 404 頁面
│
├── 📦 src/
│   ├── components/                 # UI 組件
│   │   ├── common/                 # 通用組件
│   │   │   ├── Button.tsx
│   │   │   ├── Card.tsx
│   │   │   ├── Modal.tsx
│   │   │   ├── Header.tsx          # 包含個人資料按鈕的頂部欄
│   │   │   └── LoadingSpinner.tsx
│   │   │
│   │   ├── question/               # 題目相關組件
│   │   │   ├── QuestionCard.tsx    # 題目卡片
│   │   │   ├── QuestionImage.tsx   # 題目圖片顯示
│   │   │   ├── OptionsList.tsx     # 選項列表（使用 FlashList）
│   │   │   ├── ProgressBar.tsx     # 答題進度條
│   │   │   └── AnswerFeedback.tsx  # 答案反饋（對錯提示）
│   │   │
│   │   ├── dashboard/              # 主頁組件
│   │   │   ├── VolumeProgressCard.tsx  # 各冊進度卡片
│   │   │   ├── QuickStats.tsx          # 快速統計
│   │   │   ├── PredictedPassRate.tsx   # 預測合格率組件
│   │   │   └── ActionButtons.tsx       # 練習/考試按鈕
│   │   │
│   │   └── history/                # 歷史記錄組件
│   │       ├── ExamHistoryList.tsx     # 使用 FlashList 顯示歷史
│   │       ├── ExamHistoryCard.tsx     # 單個考試記錄卡片
│   │       └── ExamDetailView.tsx      # 考試詳情視圖
│   │
│   ├── services/                   # 服務層
│   │   ├── database/               
│   │   │   ├── init.ts             # SQLite 初始化
│   │   │   ├── schema.ts           # 數據表結構定義
│   │   │   ├── answerService.ts   # 答題記錄服務（實時寫入）
│   │   │   ├── sessionService.ts  # 會話管理服務
│   │   │   └── queries.ts         # 通用查詢函數
│   │   │
│   │   ├── questions/              
│   │   │   ├── loader.ts           # 加載題庫 JSON
│   │   │   ├── manager.ts          # 題目管理（篩選、隨機等）
│   │   │   └── shuffler.ts         # 使用 lodash/shuffle
│   │   │
│   │   ├── statistics/             
│   │   │   ├── calculator.ts       # 統計計算
│   │   │   ├── tracker.ts          # 進度追蹤
│   │   │   └── predictor.ts        # 合格率預測算法
│   │   │
│   │   └── storage/                
│   │       └── preferences.ts      # MMKV 用戶偏好設置
│   │
│   ├── store/                      # Zustand 狀態管理
│   │   ├── useAppStore.ts          # 全局應用狀態
│   │   ├── usePracticeStore.ts     # 練習模式狀態
│   │   ├── useExamStore.ts         # 考試模式狀態
│   │   └── usePreferencesStore.ts  # 用戶偏好（使用 MMKV 持久化）
│   │
│   ├── hooks/                      # 自定義 Hooks
│   │   ├── useDatabase.ts          # 數據庫操作
│   │   ├── useQuestions.ts         # 題目相關
│   │   ├── useStatistics.ts        # 統計數據
│   │   ├── useExamHistory.ts       # 考試歷史
│   │   └── useRealtimeAnswer.ts    # 實時答題記錄
│   │
│   ├── utils/                      
│   │   ├── constants.ts            # 常量（考試規則等）
│   │   ├── helpers.ts              # 輔助函數
│   │   ├── dateFormat.ts           # 日期格式化
│   │   └── performance.ts          # 性能監控工具
│   │
│   └── types/                      # TypeScript 類型
│       ├── question.ts
│       ├── database.ts
│       ├── session.ts
│       └── statistics.ts
│
├── 📸 assets/
│   ├── images/
│   │   ├── questions/              # 題目圖片
│   │   │   ├── volume1/           # 第1冊圖片
│   │   │   ├── volume2/           # 第2冊圖片
│   │   │   ├── volume3/           # 第3冊圖片
│   │   │   ├── volume4/           # 第4冊圖片
│   │   │   └── volume5/           # 第5冊圖片
│   │   ├── icons/                  # UI 圖標
│   │   └── animations/             # Lottie 動畫文件
│   │       ├── success.json       # 成功動畫
│   │       ├── fail.json          # 失敗動畫
│   │       └── achievement.json   # 成就動畫
│   │
│   └── data/                       # 題庫數據
│       ├── questions/
│       │   ├── volume1.json       # 第1冊題庫
│       │   ├── volume2.json       # 第2冊題庫
│       │   ├── volume3.json       # 第3冊題庫
│       │   ├── volume4.json       # 第4冊題庫
│       │   └── volume5.json       # 第5冊題庫
│       └── exam-rules.json        # 考試規則配置
│
├── 📦 package.json
├── 📄 tsconfig.json
├── 📄 app.json
└── 📄 README.md
```

## 🛠 技術棧說明

### 核心依賴

```json
{
  "dependencies": {
    // Expo 核心
    "expo": "~51.0.0",
    "expo-router": "~3.5.0",
    "expo-sqlite": "~14.0.0",
    
    // 狀態管理與存儲
    "zustand": "^4.5.0",
    "react-native-mmkv": "^2.11.0",    // 快速鍵值存儲（用戶偏好）
    
    // UI 組件與動畫
    "react-native-reanimated": "~3.10.0",
    "react-native-gesture-handler": "~2.16.0",
    "react-native-safe-area-context": "4.10.0",
    "@shopify/flash-list": "1.6.4",    // 高性能列表
    "lottie-react-native": "6.7.0",    // 動畫效果
    
    // 工具
    "lodash.shuffle": "^4.2.0",        // 只引入 shuffle
    "date-fns": "^3.0.0",              // 日期處理
    "uuid": "^9.0.0"                   // 生成會話ID
  }
}
```

## 💾 優化的數據庫設計

```sql
-- 1. 答題記錄表（每題實時寫入）
CREATE TABLE answer_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    question_id INTEGER NOT NULL,
    volume INTEGER NOT NULL,           -- 冊數 (1-5)
    chapter INTEGER NOT NULL,          -- 章節
    is_correct BOOLEAN NOT NULL,
    mode TEXT NOT NULL,                -- 'practice' | 'exam'
    session_id TEXT NOT NULL,          -- 會話ID (UUID)
    selected_option TEXT,              -- 用戶選擇 (A/B/C/D)
    correct_option TEXT,               -- 正確答案 (A/B/C/D)
    time_spent INTEGER,                -- 答題用時（秒）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 2. 練習/考試會話表
CREATE TABLE sessions (
    id TEXT PRIMARY KEY,               -- UUID
    type TEXT NOT NULL,                -- 'practice' | 'exam'
    title TEXT,                        -- 會話標題（如：第1冊練習）
    total_questions INTEGER,
    correct_count INTEGER DEFAULT 0,
    wrong_count INTEGER DEFAULT 0,
    duration_seconds INTEGER,
    volumes TEXT,                      -- JSON array [1,2,3]
    config TEXT,                       -- JSON 練習設定或考試規則
    is_completed BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME
);

-- 3. 用戶進度表（按冊統計）
CREATE TABLE volume_progress (
    volume INTEGER PRIMARY KEY,
    total_questions INTEGER,           -- 該冊總題數
    seen_count INTEGER DEFAULT 0,      -- 已看過的題目數
    correct_count INTEGER DEFAULT 0,
    wrong_count INTEGER DEFAULT 0,
    last_practice DATETIME,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 4. 題目統計表（追蹤每道題的表現）
CREATE TABLE question_stats (
    question_id INTEGER PRIMARY KEY,
    volume INTEGER NOT NULL,
    total_attempts INTEGER DEFAULT 0,
    correct_attempts INTEGER DEFAULT 0,
    wrong_attempts INTEGER DEFAULT 0,
    last_attempted DATETIME,
    is_bookmarked BOOLEAN DEFAULT 0,
    note TEXT                          -- 用戶筆記
);

-- 索引優化查詢性能
CREATE INDEX idx_records_session ON answer_records(session_id);
CREATE INDEX idx_records_question ON answer_records(question_id);
CREATE INDEX idx_records_created ON answer_records(created_at);
CREATE INDEX idx_sessions_type ON sessions(type);
CREATE INDEX idx_sessions_created ON sessions(created_at DESC);
```

## 🔄 關鍵實現細節

### 1. 實時答題記錄（性能優化）

```typescript
// src/hooks/useRealtimeAnswer.ts
export function useRealtimeAnswer() {
  const db = useDatabase();
  
  // 使用事務批量寫入，但保持實時性
  const recordAnswer = useCallback(async (answer: AnswerData) => {
    // 單條記錄直接寫入，SQLite 本地寫入很快
    await db.transaction(async (tx) => {
      await tx.executeSql(
        `INSERT INTO answer_records 
         (question_id, volume, chapter, is_correct, mode, session_id, 
          selected_option, correct_option, time_spent) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [answer.questionId, answer.volume, ...]
      );
      
      // 同時更新統計表
      await tx.executeSql(
        `UPDATE question_stats 
         SET total_attempts = total_attempts + 1,
             ${answer.isCorrect ? 'correct_attempts' : 'wrong_attempts'} = 
             ${answer.isCorrect ? 'correct_attempts' : 'wrong_attempts'} + 1
         WHERE question_id = ?`,
        [answer.questionId]
      );
    });
  }, [db]);
  
  return { recordAnswer };
}
```

### 2. 動態路由實現（查看歷史考試）

```typescript
// app/exam/history/[sessionId].tsx
import { useLocalSearchParams } from 'expo-router';
import { useExamHistory } from '@/hooks/useExamHistory';

export default function ExamDetailScreen() {
  const { sessionId } = useLocalSearchParams<{ sessionId: string }>();
  const { examDetail, answers } = useExamHistory(sessionId);
  
  return (
    <FlashList
      data={answers}
      renderItem={({ item }) => (
        <QuestionReviewCard
          question={item.question}
          userAnswer={item.selected_option}
          correctAnswer={item.correct_option}
          isCorrect={item.is_correct}
        />
      )}
      estimatedItemSize={150}
    />
  );
}
```

### 3. MMKV vs SQLite 分工

```typescript
// src/services/storage/preferences.ts
import { MMKV } from 'react-native-mmkv';

const storage = new MMKV();

// MMKV 用於：
// - 用戶設置（主題、字體大小等）
// - 臨時狀態（當前練習進度）
// - 快速訪問的配置

export const Preferences = {
  // 立即讀寫，無需異步
  setTheme: (theme: string) => storage.set('theme', theme),
  getTheme: () => storage.getString('theme') ?? 'light',
  
  setLastPracticeVolume: (volume: number) => storage.set('lastVolume', volume),
  getLastPracticeVolume: () => storage.getNumber('lastVolume') ?? 1,
};

// SQLite 用於：
// - 答題記錄（需要複雜查詢）
// - 統計數據（需要聚合計算）
// - 歷史記錄（需要關聯查詢）
```

### 4. FlashList 優化列表性能

```typescript
// src/components/history/ExamHistoryList.tsx
import { FlashList } from '@shopify/flash-list';

export function ExamHistoryList({ sessions }: Props) {
  return (
    <FlashList
      data={sessions}
      renderItem={({ item }) => (
        <ExamHistoryCard
          session={item}
          onPress={() => router.push(`/exam/history/${item.id}`)}
        />
      )}
      estimatedItemSize={100}  // 預估項目高度
      keyExtractor={(item) => item.id}
      ListEmptyComponent={<EmptyState />}
    />
  );
}
```

### 5. Lottie 動畫集成

```typescript
// src/components/common/ResultAnimation.tsx
import LottieView from 'lottie-react-native';

export function ResultAnimation({ passed }: { passed: boolean }) {
  return (
    <LottieView
      source={passed ? require('@/assets/animations/success.json') 
                     : require('@/assets/animations/fail.json')}
      autoPlay
      loop={false}
      style={{ width: 200, height: 200 }}
    />
  );
}
```

## 📊 性能考慮

### SQLite 實時寫入性能
- **單條記錄寫入**：本地 SQLite 寫入速度約 1-5ms
- **用戶體驗**：完全不會有感知的延遲
- **優化建議**：
  - 使用事務包裹多個操作
  - 創建適當的索引
  - 定期執行 VACUUM 優化數據庫

### MMKV vs SQLite 使用原則
| 特性 | MMKV | SQLite |
|-----|------|--------|
| 用途 | 簡單鍵值對、用戶偏好 | 結構化數據、需要查詢 |
| 速度 | 極快（同步） | 快（異步） |
| 查詢 | 不支持 | 完整 SQL |
| 大小限制 | 適合小數據 | 適合大數據 |

## 🚀 實施建議

### MVP 第一版（1週）
1. ✅ 基本練習流程
2. ✅ 實時答題記錄
3. ✅ 簡單統計顯示

### 第二版（1週）
1. ✅ 完整考試模式
2. ✅ 歷史記錄查看
3. ✅ 錯題集功能

### 第三版（優化）
1. ✅ 動畫效果
2. ✅ 性能優化
3. ✅ 預測合格率

這個架構完全支持你的需求，並遵循了 React Native 和 Expo Router 的最佳實踐。